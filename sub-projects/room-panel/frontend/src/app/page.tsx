'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  CalendarDays, 
  Users, 
  MapPin, 
  BarChart3,
  ArrowRight,
  Building2,
  Clock
} from 'lucide-react'
import Link from 'next/link'

export default function HomePage() {
  const router = useRouter()

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <Building2 className="h-8 w-8 text-blue-600" />
              <h1 className="text-xl font-bold text-gray-900">智能门牌管理系统</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" asChild>
                <Link href="/login">登录</Link>
              </Button>
              <Button asChild>
                <Link href="/dashboard">进入管理后台</Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* 欢迎区域 */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            智能会议室管理解决方案
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            通过智能门牌系统，轻松管理会议室预订、实时状态监控、扫码签到等功能，
            提升会议室使用效率，优化办公体验。
          </p>
          <div className="flex justify-center space-x-4">
            <Button size="lg" asChild>
              <Link href="/dashboard">
                开始使用
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/rooms">查看会议室</Link>
            </Button>
          </div>
        </div>

        {/* 功能特性 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <MapPin className="h-6 w-6 text-blue-600" />
                <CardTitle>会议室管理</CardTitle>
              </div>
              <CardDescription>
                统一管理所有会议室信息，包括位置、容量、设备等
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 会议室基本信息管理</li>
                <li>• 实时状态监控</li>
                <li>• 设备配置管理</li>
              </ul>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <CalendarDays className="h-6 w-6 text-green-600" />
                <CardTitle>会议安排</CardTitle>
              </div>
              <CardDescription>
                便捷的会议预订和管理，支持重复会议和冲突检测
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 会议预订和取消</li>
                <li>• 时间冲突检测</li>
                <li>• 企业微信集成</li>
              </ul>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Users className="h-6 w-6 text-purple-600" />
                <CardTitle>签到管理</CardTitle>
              </div>
              <CardDescription>
                二维码扫码签到，实时统计参会人员
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 二维码扫码签到</li>
                <li>• 签到统计分析</li>
                <li>• 参会人员管理</li>
              </ul>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <BarChart3 className="h-6 w-6 text-orange-600" />
                <CardTitle>数据统计</CardTitle>
              </div>
              <CardDescription>
                详细的使用统计和分析报告
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 会议室使用率统计</li>
                <li>• 签到率分析</li>
                <li>• 使用趋势报告</li>
              </ul>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Clock className="h-6 w-6 text-red-600" />
                <CardTitle>实时监控</CardTitle>
              </div>
              <CardDescription>
                实时显示会议室状态和使用情况
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 实时状态更新</li>
                <li>• 门牌显示控制</li>
                <li>• 异常状态提醒</li>
              </ul>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Building2 className="h-6 w-6 text-indigo-600" />
                <CardTitle>多终端支持</CardTitle>
              </div>
              <CardDescription>
                支持平板门牌、手机APP、Web管理后台
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 平板门牌显示</li>
                <li>• 手机扫码签到</li>
                <li>• Web管理后台</li>
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* 快速开始 */}
        <div className="bg-white rounded-lg shadow-lg p-8 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            立即开始使用智能门牌系统
          </h3>
          <p className="text-gray-600 mb-6">
            简单配置，快速部署，让您的会议室管理更加智能化
          </p>
          <Button size="lg" asChild>
            <Link href="/dashboard">
              进入管理后台
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </main>

      {/* 页脚 */}
      <footer className="bg-gray-50 border-t mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-600">
            <p>&copy; 2024 智能门牌管理系统. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
