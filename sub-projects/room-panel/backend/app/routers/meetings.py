from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from datetime import datetime, date

from app.database import get_db
from app.models import Meeting as MeetingModel, Room as RoomModel
from app.schemas import Meeting, MeetingCreate, MeetingUpdate, MeetingWithCheckIns
from app.routers.auth import get_current_active_user

router = APIRouter()

@router.post("/", response_model=Meeting)
async def create_meeting(
    meeting: MeetingCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """创建会议"""
    # 检查会议室是否存在
    room = db.query(RoomModel).filter(RoomModel.id == meeting.room_id).first()
    if not room:
        raise HTTPException(status_code=404, detail="Room not found")
    
    # 检查时间冲突
    conflicting_meeting = db.query(MeetingModel).filter(
        MeetingModel.room_id == meeting.room_id,
        MeetingModel.start_time < meeting.end_time,
        MeetingModel.end_time > meeting.start_time,
        MeetingModel.status.in_(["scheduled", "ongoing"])
    ).first()
    
    if conflicting_meeting:
        raise HTTPException(
            status_code=400, 
            detail="Meeting time conflicts with existing meeting"
        )
    
    db_meeting = MeetingModel(**meeting.dict())
    db.add(db_meeting)
    db.commit()
    db.refresh(db_meeting)
    return db_meeting

@router.get("/", response_model=List[Meeting])
async def read_meetings(
    skip: int = 0,
    limit: int = 100,
    room_id: int = None,
    start_date: date = None,
    end_date: date = None,
    db: Session = Depends(get_db)
):
    """获取会议列表"""
    query = db.query(MeetingModel)
    
    if room_id:
        query = query.filter(MeetingModel.room_id == room_id)
    
    if start_date:
        query = query.filter(MeetingModel.start_time >= start_date)
    
    if end_date:
        query = query.filter(MeetingModel.start_time <= end_date)
    
    meetings = query.offset(skip).limit(limit).all()
    return meetings

@router.get("/{meeting_id}", response_model=MeetingWithCheckIns)
async def read_meeting(meeting_id: int, db: Session = Depends(get_db)):
    """获取单个会议详情"""
    meeting = db.query(MeetingModel).filter(MeetingModel.id == meeting_id).first()
    if meeting is None:
        raise HTTPException(status_code=404, detail="Meeting not found")
    return meeting

@router.put("/{meeting_id}", response_model=Meeting)
async def update_meeting(
    meeting_id: int,
    meeting_update: MeetingUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """更新会议信息"""
    meeting = db.query(MeetingModel).filter(MeetingModel.id == meeting_id).first()
    if meeting is None:
        raise HTTPException(status_code=404, detail="Meeting not found")
    
    update_data = meeting_update.dict(exclude_unset=True)
    
    # 如果更新时间，检查冲突
    if "start_time" in update_data or "end_time" in update_data:
        start_time = update_data.get("start_time", meeting.start_time)
        end_time = update_data.get("end_time", meeting.end_time)
        
        conflicting_meeting = db.query(MeetingModel).filter(
            MeetingModel.room_id == meeting.room_id,
            MeetingModel.id != meeting_id,
            MeetingModel.start_time < end_time,
            MeetingModel.end_time > start_time,
            MeetingModel.status.in_(["scheduled", "ongoing"])
        ).first()
        
        if conflicting_meeting:
            raise HTTPException(
                status_code=400, 
                detail="Meeting time conflicts with existing meeting"
            )
    
    for field, value in update_data.items():
        setattr(meeting, field, value)
    
    db.commit()
    db.refresh(meeting)
    return meeting

@router.delete("/{meeting_id}")
async def delete_meeting(
    meeting_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """删除会议"""
    meeting = db.query(MeetingModel).filter(MeetingModel.id == meeting_id).first()
    if meeting is None:
        raise HTTPException(status_code=404, detail="Meeting not found")
    
    db.delete(meeting)
    db.commit()
    return {"message": "Meeting deleted successfully"}

@router.post("/{meeting_id}/start")
async def start_meeting(
    meeting_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """开始会议"""
    meeting = db.query(MeetingModel).filter(MeetingModel.id == meeting_id).first()
    if meeting is None:
        raise HTTPException(status_code=404, detail="Meeting not found")
    
    meeting.status = "ongoing"
    db.commit()
    db.refresh(meeting)
    return meeting

@router.post("/{meeting_id}/end")
async def end_meeting(
    meeting_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """结束会议"""
    meeting = db.query(MeetingModel).filter(MeetingModel.id == meeting_id).first()
    if meeting is None:
        raise HTTPException(status_code=404, detail="Meeting not found")
    
    meeting.status = "completed"
    db.commit()
    db.refresh(meeting)
    return meeting
