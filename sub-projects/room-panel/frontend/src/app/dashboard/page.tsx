'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  CalendarDays, 
  Users, 
  MapPin, 
  Clock,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  XCircle
} from 'lucide-react'
import Link from 'next/link'
import { useQuery } from 'react-query'
import { getDashboardStats } from '@/lib/api'

export default function DashboardPage() {
  const { data: stats, isLoading } = useQuery('dashboard-stats', getDashboardStats)

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-1/3 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">仪表板概览</h1>
        <p className="text-gray-600">智能门牌系统运营数据总览</p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总会议室数</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalRooms || 0}</div>
            <p className="text-xs text-muted-foreground">
              可用: {stats?.availableRooms || 0} | 使用中: {stats?.occupiedRooms || 0}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日会议</CardTitle>
            <CalendarDays className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.todayMeetings || 0}</div>
            <p className="text-xs text-muted-foreground">
              进行中: {stats?.ongoingMeetings || 0} | 已完成: {stats?.completedMeetings || 0}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">签到人数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.todayCheckins || 0}</div>
            <p className="text-xs text-muted-foreground">
              签到率: {stats?.checkinRate || 0}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">使用率</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.utilizationRate || 0}%</div>
            <p className="text-xs text-muted-foreground">
              较昨日 {stats?.utilizationChange > 0 ? '+' : ''}{stats?.utilizationChange || 0}%
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 实时状态 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 会议室状态 */}
        <Card>
          <CardHeader>
            <CardTitle>会议室实时状态</CardTitle>
            <CardDescription>当前所有会议室的使用情况</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats?.roomStatus?.map((room: any) => (
                <div key={room.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${
                      room.status === 'available' ? 'bg-green-500' :
                      room.status === 'occupied' ? 'bg-red-500' : 'bg-yellow-500'
                    }`} />
                    <div>
                      <p className="font-medium">{room.name}</p>
                      <p className="text-sm text-gray-500">{room.location}</p>
                    </div>
                  </div>
                  <Badge variant={
                    room.status === 'available' ? 'default' :
                    room.status === 'occupied' ? 'destructive' : 'secondary'
                  }>
                    {room.status === 'available' ? '空闲' :
                     room.status === 'occupied' ? '使用中' : '维护中'}
                  </Badge>
                </div>
              )) || (
                <div className="text-center py-8 text-gray-500">
                  暂无会议室数据
                </div>
              )}
            </div>
            <div className="mt-4">
              <Button variant="outline" size="sm" asChild>
                <Link href="/dashboard/rooms">查看全部会议室</Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 今日会议 */}
        <Card>
          <CardHeader>
            <CardTitle>今日会议安排</CardTitle>
            <CardDescription>今天的会议时间表</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats?.todayMeetingList?.map((meeting: any) => (
                <div key={meeting.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="font-medium">{meeting.title}</p>
                      <p className="text-sm text-gray-500">
                        {meeting.timeRange} | {meeting.roomName}
                      </p>
                    </div>
                  </div>
                  <Badge variant={
                    meeting.status === 'ongoing' ? 'default' :
                    meeting.status === 'completed' ? 'secondary' :
                    meeting.status === 'cancelled' ? 'destructive' : 'outline'
                  }>
                    {meeting.status === 'ongoing' ? '进行中' :
                     meeting.status === 'completed' ? '已结束' :
                     meeting.status === 'cancelled' ? '已取消' : '待开始'}
                  </Badge>
                </div>
              )) || (
                <div className="text-center py-8 text-gray-500">
                  今日暂无会议安排
                </div>
              )}
            </div>
            <div className="mt-4">
              <Button variant="outline" size="sm" asChild>
                <Link href="/dashboard/meetings">查看全部会议</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 快速操作 */}
      <Card>
        <CardHeader>
          <CardTitle>快速操作</CardTitle>
          <CardDescription>常用功能快捷入口</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" asChild>
              <Link href="/dashboard/rooms/new">
                <MapPin className="mr-2 h-4 w-4" />
                添加会议室
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/dashboard/meetings/new">
                <CalendarDays className="mr-2 h-4 w-4" />
                创建会议
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/dashboard/analytics">
                <TrendingUp className="mr-2 h-4 w-4" />
                查看统计
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/dashboard/settings">
                <AlertCircle className="mr-2 h-4 w-4" />
                系统设置
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
