import 'package:json_annotation/json_annotation.dart';
import 'room.dart';

part 'meeting.g.dart';

@JsonSerializable()
class Meeting {
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'room_id')
  final int roomId;
  final String title;
  final String? organizer;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'start_time')
  final DateTime startTime;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'end_time')
  final DateTime endTime;
  final String? attendees;
  final String status;
  @JsonKey(name: 'wework_meeting_id')
  final String? weworkMeetingId;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;
  final Room? room;

  Meeting({
    required this.id,
    required this.roomId,
    required this.title,
    this.organizer,
    required this.startTime,
    required this.endTime,
    this.attendees,
    required this.status,
    this.weworkMeetingId,
    required this.createdAt,
    this.updatedAt,
    this.room,
  });

  factory Meeting.fromJson(Map<String, dynamic> json) => _$MeetingFromJson(json);
  Map<String, dynamic> toJson() => _$MeetingToJson(this);

  // 获取状态显示文本
  String get statusText {
    switch (status) {
      case 'scheduled':
        return '已安排';
      case 'ongoing':
        return '进行中';
      case 'completed':
        return '已结束';
      case 'cancelled':
        return '已取消';
      default:
        return '未知';
    }
  }

  // 获取状态颜色
  int get statusColor {
    switch (status) {
      case 'scheduled':
        return 0xFF2196F3; // 蓝色
      case 'ongoing':
        return 0xFF4CAF50; // 绿色
      case 'completed':
        return 0xFF9E9E9E; // 灰色
      case 'cancelled':
        return 0xFFFF5722; // 红色
      default:
        return 0xFF9E9E9E; // 灰色
    }
  }

  // 会议时长（分钟）
  int get durationInMinutes {
    return endTime.difference(startTime).inMinutes;
  }

  // 格式化时间范围
  String get timeRange {
    final startStr = '${startTime.hour.toString().padLeft(2, '0')}:${startTime.minute.toString().padLeft(2, '0')}';
    final endStr = '${endTime.hour.toString().padLeft(2, '0')}:${endTime.minute.toString().padLeft(2, '0')}';
    return '$startStr - $endStr';
  }

  // 是否正在进行
  bool get isOngoing {
    final now = DateTime.now();
    return status == 'ongoing' || (startTime.isBefore(now) && endTime.isAfter(now));
  }

  // 是否即将开始（15分钟内）
  bool get isStartingSoon {
    final now = DateTime.now();
    final diff = startTime.difference(now).inMinutes;
    return diff > 0 && diff <= 15 && status == 'scheduled';
  }

  // 是否已结束
  bool get isCompleted {
    return status == 'completed' || endTime.isBefore(DateTime.now());
  }

  // 解析参会人员
  List<String> get attendeesList {
    if (attendees == null || attendees!.isEmpty) return [];
    try {
      // 假设attendees是逗号分隔的字符串或JSON数组
      if (attendees!.startsWith('[')) {
        // JSON数组格式
        return attendees!.replaceAll(RegExp(r'[\[\]"]'), '').split(',')
            .map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
      } else {
        // 逗号分隔格式
        return attendees!.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
      }
    } catch (e) {
      return [];
    }
  }

  // 复制对象
  Meeting copyWith({
    int? id,
    int? roomId,
    String? title,
    String? organizer,
    DateTime? startTime,
    DateTime? endTime,
    String? attendees,
    String? status,
    String? weworkMeetingId,
    DateTime? createdAt,
    DateTime? updatedAt,
    Room? room,
  }) {
    return Meeting(
      id: id ?? this.id,
      roomId: roomId ?? this.roomId,
      title: title ?? this.title,
      organizer: organizer ?? this.organizer,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      attendees: attendees ?? this.attendees,
      status: status ?? this.status,
      weworkMeetingId: weworkMeetingId ?? this.weworkMeetingId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      room: room ?? this.room,
    );
  }

  @override
  String toString() {
    return 'Meeting{id: $id, title: $title, status: $status, timeRange: $timeRange}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Meeting && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}
