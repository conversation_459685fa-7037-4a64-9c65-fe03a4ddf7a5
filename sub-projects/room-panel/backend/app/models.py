from sqlalchemy import Column, Integer, String, DateTime, <PERSON><PERSON><PERSON>, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base

class Room(Base):
    """会议室模型"""
    __tablename__ = "rooms"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="会议室名称")
    location = Column(String(200), comment="会议室位置")
    capacity = Column(Integer, comment="容纳人数")
    equipment = Column(Text, comment="设备信息")
    status = Column(String(20), default="available", comment="状态: available, occupied, maintenance")
    qr_code = Column(String(500), comment="二维码内容")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联会议
    meetings = relationship("Meeting", back_populates="room")

class Meeting(Base):
    """会议模型"""
    __tablename__ = "meetings"
    
    id = Column(Integer, primary_key=True, index=True)
    room_id = Column(Integer, ForeignKey("rooms.id"), nullable=False)
    title = Column(String(200), nullable=False, comment="会议标题")
    organizer = Column(String(100), comment="组织者")
    start_time = Column(DateTime(timezone=True), nullable=False, comment="开始时间")
    end_time = Column(DateTime(timezone=True), nullable=False, comment="结束时间")
    attendees = Column(Text, comment="参会人员，JSON格式")
    status = Column(String(20), default="scheduled", comment="状态: scheduled, ongoing, completed, cancelled")
    wework_meeting_id = Column(String(100), comment="企业微信会议ID")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联会议室
    room = relationship("Room", back_populates="meetings")
    # 关联签到记录
    checkins = relationship("CheckIn", back_populates="meeting")

class CheckIn(Base):
    """签到记录模型"""
    __tablename__ = "checkins"
    
    id = Column(Integer, primary_key=True, index=True)
    meeting_id = Column(Integer, ForeignKey("meetings.id"), nullable=False)
    user_name = Column(String(100), nullable=False, comment="用户姓名")
    user_id = Column(String(100), comment="用户ID")
    checkin_time = Column(DateTime(timezone=True), server_default=func.now(), comment="签到时间")
    checkin_method = Column(String(20), default="qrcode", comment="签到方式: qrcode, manual")
    
    # 关联会议
    meeting = relationship("Meeting", back_populates="checkins")

class User(Base):
    """用户模型"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, nullable=False, comment="用户名")
    email = Column(String(100), unique=True, comment="邮箱")
    hashed_password = Column(String(100), comment="密码哈希")
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_admin = Column(Boolean, default=False, comment="是否管理员")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
