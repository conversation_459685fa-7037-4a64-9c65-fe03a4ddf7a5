#!/bin/bash

# 智能门牌系统前端启动脚本

echo "正在启动智能门牌系统前端..."

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "错误: Node.js未安装"
    echo "请先安装Node.js 18+版本"
    exit 1
fi

echo "Node.js版本:"
node --version

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "错误: npm未安装"
    exit 1
fi

echo "npm版本:"
npm --version

# 安装依赖
if [ ! -d "node_modules" ]; then
    echo "安装依赖包..."
    npm install
else
    echo "依赖包已存在，跳过安装"
fi

# 检查环境变量文件
if [ ! -f ".env.local" ]; then
    echo "创建环境变量文件..."
    cat > .env.local << EOF
# API配置
NEXT_PUBLIC_API_URL=http://localhost:8000

# 其他配置
NEXT_PUBLIC_APP_NAME=智能门牌管理系统
NEXT_PUBLIC_APP_VERSION=1.0.0
EOF
    echo "请根据需要编辑 .env.local 文件"
fi

# 启动开发服务器
echo "启动开发服务器..."
echo "访问地址: http://localhost:3000"
npm run dev
