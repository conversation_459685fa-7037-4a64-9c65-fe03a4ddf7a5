// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'checkin.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CheckIn _$CheckInFromJson(Map<String, dynamic> json) => CheckIn(
      id: (json['id'] as num).toInt(),
      meetingId: (json['meeting_id'] as num).toInt(),
      userName: json['user_name'] as String,
      userId: json['user_id'] as String?,
      checkinTime: DateTime.parse(json['checkin_time'] as String),
      checkinMethod: json['checkin_method'] as String,
      meeting: json['meeting'] == null
          ? null
          : Meeting.fromJson(json['meeting'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CheckInToJson(CheckIn instance) => <String, dynamic>{
      'id': instance.id,
      'meeting_id': instance.meetingId,
      'user_name': instance.userName,
      'user_id': instance.userId,
      'checkin_time': instance.checkinTime.toIso8601String(),
      'checkin_method': instance.checkinMethod,
      'meeting': instance.meeting,
    };

CheckInRequest _$CheckInRequestFromJson(Map<String, dynamic> json) =>
    CheckInRequest(
      meetingId: (json['meeting_id'] as num).toInt(),
      userName: json['user_name'] as String,
      userId: json['user_id'] as String?,
      checkinMethod: json['checkin_method'] as String? ?? 'qrcode',
    );

Map<String, dynamic> _$CheckInRequestToJson(CheckInRequest instance) =>
    <String, dynamic>{
      'meeting_id': instance.meetingId,
      'user_name': instance.userName,
      'user_id': instance.userId,
      'checkin_method': instance.checkinMethod,
    };
