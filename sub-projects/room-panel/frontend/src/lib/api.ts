import axios from 'axios'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

const api = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('access_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// 类型定义
export interface Room {
  id: number
  name: string
  location?: string
  capacity?: number
  equipment?: string
  status: 'available' | 'occupied' | 'maintenance'
  qr_code?: string
  created_at: string
  updated_at?: string
}

export interface Meeting {
  id: number
  room_id: number
  title: string
  organizer?: string
  start_time: string
  end_time: string
  attendees?: string
  status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled'
  wework_meeting_id?: string
  created_at: string
  updated_at?: string
  room?: Room
}

export interface CheckIn {
  id: number
  meeting_id: number
  user_name: string
  user_id?: string
  checkin_time: string
  checkin_method: 'qrcode' | 'manual'
  meeting?: Meeting
}

export interface User {
  id: number
  username: string
  email?: string
  is_active: boolean
  is_admin: boolean
  created_at: string
  updated_at?: string
}

// API 函数

// 认证相关
export const login = async (username: string, password: string) => {
  const formData = new FormData()
  formData.append('username', username)
  formData.append('password', password)
  
  const response = await api.post('/auth/token', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
  return response.data
}

export const getCurrentUser = async () => {
  const response = await api.get('/auth/me')
  return response.data
}

// 仪表板统计
export const getDashboardStats = async () => {
  // 模拟数据，实际应该调用后端API
  return {
    totalRooms: 12,
    availableRooms: 8,
    occupiedRooms: 3,
    todayMeetings: 15,
    ongoingMeetings: 3,
    completedMeetings: 8,
    todayCheckins: 45,
    checkinRate: 85,
    utilizationRate: 72,
    utilizationChange: 5,
    roomStatus: [
      { id: 1, name: '会议室A', location: '1楼', status: 'available' },
      { id: 2, name: '会议室B', location: '1楼', status: 'occupied' },
      { id: 3, name: '会议室C', location: '2楼', status: 'available' },
    ],
    todayMeetingList: [
      {
        id: 1,
        title: '项目启动会',
        timeRange: '09:00 - 10:30',
        roomName: '会议室A',
        status: 'completed'
      },
      {
        id: 2,
        title: '技术评审',
        timeRange: '14:00 - 15:30',
        roomName: '会议室B',
        status: 'ongoing'
      },
    ]
  }
}

// 会议室相关
export const getRooms = async () => {
  const response = await api.get('/rooms')
  return response.data
}

export const getRoom = async (id: number) => {
  const response = await api.get(`/rooms/${id}`)
  return response.data
}

export const createRoom = async (room: Omit<Room, 'id' | 'created_at' | 'updated_at'>) => {
  const response = await api.post('/rooms', room)
  return response.data
}

export const updateRoom = async (id: number, room: Partial<Room>) => {
  const response = await api.put(`/rooms/${id}`, room)
  return response.data
}

export const deleteRoom = async (id: number) => {
  const response = await api.delete(`/rooms/${id}`)
  return response.data
}

export const getRoomStatus = async (id: number) => {
  const response = await api.get(`/rooms/${id}/status`)
  return response.data
}

// 会议相关
export const getMeetings = async (params?: {
  room_id?: number
  start_date?: string
  end_date?: string
  skip?: number
  limit?: number
}) => {
  const response = await api.get('/meetings', { params })
  return response.data
}

export const getMeeting = async (id: number) => {
  const response = await api.get(`/meetings/${id}`)
  return response.data
}

export const createMeeting = async (meeting: Omit<Meeting, 'id' | 'created_at' | 'updated_at'>) => {
  const response = await api.post('/meetings', meeting)
  return response.data
}

export const updateMeeting = async (id: number, meeting: Partial<Meeting>) => {
  const response = await api.put(`/meetings/${id}`, meeting)
  return response.data
}

export const deleteMeeting = async (id: number) => {
  const response = await api.delete(`/meetings/${id}`)
  return response.data
}

export const startMeeting = async (id: number) => {
  const response = await api.post(`/meetings/${id}/start`)
  return response.data
}

export const endMeeting = async (id: number) => {
  const response = await api.post(`/meetings/${id}/end`)
  return response.data
}

// 签到相关
export const getCheckIns = async (meetingId: number) => {
  const response = await api.get(`/qrcode/checkin/meeting/${meetingId}`)
  return response.data
}

export const createCheckIn = async (checkIn: {
  meeting_id: number
  user_name: string
  user_id?: string
  checkin_method?: 'qrcode' | 'manual'
}) => {
  const response = await api.post('/qrcode/checkin', checkIn)
  return response.data
}

// 用户相关
export const getUsers = async () => {
  // 模拟数据，实际应该有用户管理API
  return []
}

export default api
