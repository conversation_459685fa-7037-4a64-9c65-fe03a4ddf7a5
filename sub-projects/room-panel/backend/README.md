# 智能门牌系统后端

基于 FastAPI 的智能门牌系统后端服务，支持企业微信会议API集成、会议室管理、预定查询、扫码签到等功能。

## 功能特性

- 🏢 会议室管理（增删改查）
- 📅 会议管理（创建、更新、取消）
- 🔗 企业微信会议API集成
- 📱 二维码生成和扫码签到
- 👤 用户认证和权限管理
- 📊 会议统计和报表
- 🔄 实时状态更新

## 技术栈

- **框架**: FastAPI
- **数据库**: PostgreSQL
- **ORM**: SQLAlchemy
- **认证**: JWT
- **API文档**: Swagger/OpenAPI
- **容器化**: Docker

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd room-panel/backend

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑配置文件
vim .env
```

配置以下环境变量：
- `DATABASE_URL`: PostgreSQL数据库连接字符串
- `WEWORK_CORP_ID`: 企业微信企业ID
- `WEWORK_CORP_SECRET`: 企业微信应用密钥
- `SECRET_KEY`: JWT密钥

### 3. 启动服务

#### 方式一：直接启动
```bash
./start.sh
```

#### 方式二：Docker启动
```bash
docker-compose up -d
```

### 4. 访问API文档

启动后访问：
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

## API接口

### 认证相关
- `POST /api/v1/auth/token` - 登录获取令牌
- `POST /api/v1/auth/register` - 用户注册
- `GET /api/v1/auth/me` - 获取当前用户信息

### 会议室管理
- `GET /api/v1/rooms` - 获取会议室列表
- `POST /api/v1/rooms` - 创建会议室
- `GET /api/v1/rooms/{room_id}` - 获取会议室详情
- `PUT /api/v1/rooms/{room_id}` - 更新会议室
- `DELETE /api/v1/rooms/{room_id}` - 删除会议室
- `GET /api/v1/rooms/{room_id}/status` - 获取会议室状态

### 会议管理
- `GET /api/v1/meetings` - 获取会议列表
- `POST /api/v1/meetings` - 创建会议
- `GET /api/v1/meetings/{meeting_id}` - 获取会议详情
- `PUT /api/v1/meetings/{meeting_id}` - 更新会议
- `DELETE /api/v1/meetings/{meeting_id}` - 删除会议
- `POST /api/v1/meetings/{meeting_id}/start` - 开始会议
- `POST /api/v1/meetings/{meeting_id}/end` - 结束会议

### 二维码和签到
- `GET /api/v1/qrcode/room/{room_id}` - 生成会议室二维码
- `GET /api/v1/qrcode/meeting/{meeting_id}` - 生成会议二维码
- `POST /api/v1/qrcode/checkin` - 扫码签到
- `GET /api/v1/qrcode/checkin/meeting/{meeting_id}` - 获取会议签到记录

## 数据库模型

### Room (会议室)
- id: 主键
- name: 会议室名称
- location: 位置
- capacity: 容纳人数
- equipment: 设备信息
- status: 状态 (available/occupied/maintenance)

### Meeting (会议)
- id: 主键
- room_id: 会议室ID
- title: 会议标题
- organizer: 组织者
- start_time: 开始时间
- end_time: 结束时间
- status: 状态 (scheduled/ongoing/completed/cancelled)
- wework_meeting_id: 企业微信会议ID

### CheckIn (签到记录)
- id: 主键
- meeting_id: 会议ID
- user_name: 用户姓名
- checkin_time: 签到时间
- checkin_method: 签到方式

## 企业微信集成

系统集成了企业微信会议API，支持：
- 创建企业微信会议
- 同步会议信息
- 获取参会人员
- 会议状态更新

## 开发指南

### 添加新的API端点

1. 在 `app/routers/` 目录下创建或编辑路由文件
2. 在 `app/schemas.py` 中定义请求/响应模型
3. 在 `app/models.py` 中定义数据库模型（如需要）
4. 在 `main.py` 中注册路由

### 数据库迁移

```bash
# 生成迁移文件
alembic revision --autogenerate -m "描述"

# 执行迁移
alembic upgrade head
```

## 部署

### Docker部署

```bash
# 构建镜像
docker build -t room-panel-backend .

# 运行容器
docker run -p 8000:8000 room-panel-backend
```

### 生产环境配置

1. 设置 `DEBUG=False`
2. 配置生产数据库
3. 设置强密码和密钥
4. 配置HTTPS
5. 设置日志记录

## 许可证

MIT License
