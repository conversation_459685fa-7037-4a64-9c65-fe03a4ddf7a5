import httpx
import json
import time
import hashlib
from typing import Dict, List, Optional
from datetime import datetime, timedelta

from app.config import settings

class WeWorkMeetingService:
    """企业微信会议服务"""
    
    def __init__(self):
        self.base_url = settings.WEWORK_API_BASE_URL
        self.corp_id = settings.WEWORK_CORP_ID
        self.corp_secret = settings.WEWORK_CORP_SECRET
        self.agent_id = settings.WEWORK_AGENT_ID
        self.meeting_secret = settings.WEWORK_MEETING_SECRET
        self._access_token = None
        self._token_expires_at = None
    
    async def get_access_token(self) -> str:
        """获取访问令牌"""
        if self._access_token and self._token_expires_at and datetime.now() < self._token_expires_at:
            return self._access_token
        
        url = f"{self.base_url}/cgi-bin/gettoken"
        params = {
            "corpid": self.corp_id,
            "corpsecret": self.corp_secret
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, params=params)
            data = response.json()
            
            if data.get("errcode") == 0:
                self._access_token = data["access_token"]
                # 提前5分钟过期
                self._token_expires_at = datetime.now() + timedelta(seconds=data["expires_in"] - 300)
                return self._access_token
            else:
                raise Exception(f"获取访问令牌失败: {data}")
    
    async def create_meeting(self, meeting_data: Dict) -> Dict:
        """创建会议"""
        access_token = await self.get_access_token()
        url = f"{self.base_url}/cgi-bin/meeting/create"
        params = {"access_token": access_token}
        
        # 构建会议数据
        payload = {
            "meetingid": meeting_data.get("meeting_id"),
            "subject": meeting_data.get("title"),
            "start_time": int(meeting_data.get("start_time").timestamp()),
            "end_time": int(meeting_data.get("end_time").timestamp()),
            "creator": meeting_data.get("organizer", ""),
            "attendees": meeting_data.get("attendees", []),
            "meeting_type": 0,  # 预约会议
            "settings": {
                "mute_enable_join": True,
                "allow_unmute_self": True,
                "mute_all": False,
                "host_video": True,
                "participant_video": True,
                "enable_live": False,
                "live_config": {},
                "allow_in_before_host": True,
                "auto_in_waiting_room": False,
                "allow_screen_shared_watermark": False,
                "water_mark_type": 0,
                "only_enterprise_user_allowed": False
            }
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, params=params, json=payload)
            data = response.json()
            
            if data.get("errcode") == 0:
                return data
            else:
                raise Exception(f"创建会议失败: {data}")
    
    async def get_meeting_info(self, meeting_id: str) -> Dict:
        """获取会议信息"""
        access_token = await self.get_access_token()
        url = f"{self.base_url}/cgi-bin/meeting/get"
        params = {
            "access_token": access_token,
            "meetingid": meeting_id
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, params=params)
            data = response.json()
            
            if data.get("errcode") == 0:
                return data
            else:
                raise Exception(f"获取会议信息失败: {data}")
    
    async def update_meeting(self, meeting_id: str, meeting_data: Dict) -> Dict:
        """更新会议"""
        access_token = await self.get_access_token()
        url = f"{self.base_url}/cgi-bin/meeting/update"
        params = {"access_token": access_token}
        
        payload = {
            "meetingid": meeting_id,
            "subject": meeting_data.get("title"),
            "start_time": int(meeting_data.get("start_time").timestamp()),
            "end_time": int(meeting_data.get("end_time").timestamp()),
            "attendees": meeting_data.get("attendees", [])
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, params=params, json=payload)
            data = response.json()
            
            if data.get("errcode") == 0:
                return data
            else:
                raise Exception(f"更新会议失败: {data}")
    
    async def cancel_meeting(self, meeting_id: str) -> Dict:
        """取消会议"""
        access_token = await self.get_access_token()
        url = f"{self.base_url}/cgi-bin/meeting/cancel"
        params = {"access_token": access_token}
        
        payload = {"meetingid": meeting_id}
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, params=params, json=payload)
            data = response.json()
            
            if data.get("errcode") == 0:
                return data
            else:
                raise Exception(f"取消会议失败: {data}")
    
    async def get_meeting_participants(self, meeting_id: str) -> List[Dict]:
        """获取会议参与者"""
        access_token = await self.get_access_token()
        url = f"{self.base_url}/cgi-bin/meeting/get_participants"
        params = {
            "access_token": access_token,
            "meetingid": meeting_id
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, params=params)
            data = response.json()
            
            if data.get("errcode") == 0:
                return data.get("participants", [])
            else:
                raise Exception(f"获取会议参与者失败: {data}")

# 创建全局实例
wework_service = WeWorkMeetingService()
