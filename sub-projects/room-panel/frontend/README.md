# 智能门牌系统前端管理页面

基于 Next.js 14 + shadcn/ui 开发的智能门牌系统管理后台，提供完整的会议室管理、会议安排、签到统计等功能。

## 功能特性

- 🏢 **会议室管理** - 会议室信息管理、状态监控
- 📅 **会议管理** - 会议创建、编辑、取消、状态跟踪
- 👥 **签到管理** - 签到记录查看、统计分析
- 📊 **数据统计** - 使用率统计、趋势分析
- 👤 **用户管理** - 用户权限管理
- ⚙️ **系统设置** - 系统配置管理
- 🌙 **主题切换** - 明暗主题支持
- 📱 **响应式设计** - 适配桌面和移动端

## 技术栈

- **框架**: Next.js 14 (App Router)
- **UI库**: shadcn/ui + Radix UI
- **样式**: Tailwind CSS
- **状态管理**: React Query + Zustand
- **类型检查**: TypeScript
- **图表**: Recharts
- **图标**: Lucide React

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   ├── page.tsx           # 首页
│   └── dashboard/         # 管理后台
│       ├── layout.tsx     # 仪表板布局
│       ├── page.tsx       # 仪表板首页
│       ├── rooms/         # 会议室管理
│       ├── meetings/      # 会议管理
│       ├── checkins/      # 签到管理
│       ├── users/         # 用户管理
│       ├── analytics/     # 统计分析
│       └── settings/      # 系统设置
├── components/            # 组件
│   ├── ui/               # 基础UI组件
│   ├── theme-provider.tsx # 主题提供者
│   ├── theme-toggle.tsx  # 主题切换
│   └── react-query-provider.tsx # React Query提供者
├── lib/                  # 工具库
│   ├── api.ts           # API接口
│   └── utils.ts         # 工具函数
├── hooks/               # 自定义Hooks
└── types/               # 类型定义
```

## 快速开始

### 1. 环境准备

确保已安装：
- Node.js 18+
- npm 或 yarn

### 2. 安装依赖

```bash
npm install
# 或
yarn install
```

### 3. 环境配置

复制环境变量文件：
```bash
cp .env.example .env.local
```

编辑 `.env.local` 配置后端API地址：
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
```

### 4. 启动开发服务器

```bash
# 使用启动脚本
./start.sh

# 或直接使用npm
npm run dev
```

访问 http://localhost:3000

### 5. 构建生产版本

```bash
npm run build
npm start
```

## 页面功能

### 首页
- 系统介绍和功能特性展示
- 快速导航到管理后台

### 仪表板概览
- 实时统计数据展示
- 会议室状态监控
- 今日会议安排
- 快速操作入口

### 会议室管理
- 会议室列表查看
- 会议室信息编辑
- 状态实时更新
- 搜索和筛选功能

### 会议管理
- 会议创建和编辑
- 会议状态管理
- 时间冲突检测
- 参会人员管理

### 签到管理
- 签到记录查看
- 签到统计分析
- 导出功能

### 统计分析
- 使用率统计图表
- 趋势分析
- 报表生成

### 用户管理
- 用户账户管理
- 权限分配
- 活动状态管理

### 系统设置
- 系统参数配置
- API设置
- 主题设置

## API集成

前端通过 axios 与后端API通信：

```typescript
// API配置
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL + '/api/v1',
  headers: {
    'Content-Type': 'application/json',
  },
})

// 自动添加认证头
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})
```

## 主要组件

### 仪表板布局
- 侧边栏导航
- 顶部工具栏
- 响应式设计

### 数据表格
- 排序和筛选
- 分页功能
- 批量操作

### 表单组件
- 表单验证
- 错误处理
- 自动保存

### 图表组件
- 实时数据更新
- 交互式图表
- 多种图表类型

## 状态管理

使用 React Query 进行服务器状态管理：

```typescript
// 获取会议室列表
const { data: rooms, isLoading } = useQuery('rooms', getRooms)

// 创建会议室
const createRoomMutation = useMutation(createRoom, {
  onSuccess: () => {
    queryClient.invalidateQueries('rooms')
    toast.success('会议室创建成功')
  }
})
```

## 主题系统

支持明暗主题切换：

```typescript
// 主题提供者
<ThemeProvider attribute="class" defaultTheme="system">
  {children}
</ThemeProvider>

// 主题切换
const { setTheme, theme } = useTheme()
```

## 响应式设计

使用 Tailwind CSS 实现响应式布局：

```tsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  {/* 内容 */}
</div>
```

## 部署

### Vercel部署

```bash
npm install -g vercel
vercel
```

### Docker部署

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

### 静态导出

```bash
npm run build
npm run export
```

## 开发指南

### 添加新页面

1. 在 `src/app/dashboard/` 下创建新目录
2. 添加 `page.tsx` 文件
3. 在侧边栏导航中添加链接

### 添加新组件

1. 在 `src/components/` 下创建组件文件
2. 使用 shadcn/ui 基础组件
3. 遵循组件命名规范

### API集成

1. 在 `src/lib/api.ts` 中添加API函数
2. 使用 React Query 进行数据管理
3. 添加错误处理和加载状态

### 样式开发

1. 使用 Tailwind CSS 类名
2. 遵循设计系统规范
3. 支持明暗主题

## 故障排除

### 常见问题

1. **API连接失败**
   - 检查 `.env.local` 中的API地址
   - 确认后端服务正在运行
   - 检查CORS配置

2. **样式不生效**
   - 检查 Tailwind CSS 配置
   - 确认组件导入正确
   - 清除浏览器缓存

3. **路由问题**
   - 检查 Next.js App Router 配置
   - 确认文件命名规范
   - 检查动态路由参数

### 调试技巧

- 使用 React Query Devtools 调试数据状态
- 使用浏览器开发者工具检查网络请求
- 查看 Next.js 开发服务器日志

## 性能优化

- 使用 Next.js 图片优化
- 实现代码分割和懒加载
- 优化 API 请求缓存
- 使用 React.memo 优化组件渲染

## 许可证

MIT License
