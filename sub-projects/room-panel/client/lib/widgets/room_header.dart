import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../providers/app_provider.dart';
import '../providers/room_provider.dart';
import '../utils/constants.dart';

class RoomHeader extends StatelessWidget {
  const RoomHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: AppConstants.headerHeight.h,
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        child: Row(
          children: [
            // 会议室信息
            Expanded(
              child: Consumer<RoomProvider>(
                builder: (context, roomProvider, child) {
                  final room = roomProvider.currentRoom;
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        room?.name ?? '会议室',
                        style: TextStyle(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      if (room?.location != null) ...[
                        SizedBox(height: 4.h),
                        Text(
                          room!.location!,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.white.withOpacity(0.8),
                          ),
                        ),
                      ],
                    ],
                  );
                },
              ),
            ),
            
            // 状态指示器
            Consumer<RoomProvider>(
              builder: (context, roomProvider, child) {
                return Container(
                  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                  decoration: BoxDecoration(
                    color: roomProvider.roomStatusColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20.r),
                    border: Border.all(
                      color: roomProvider.roomStatusColor,
                      width: 2,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 8.w,
                        height: 8.w,
                        decoration: BoxDecoration(
                          color: roomProvider.roomStatusColor,
                          shape: BoxShape.circle,
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        roomProvider.roomStatusText,
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
            
            SizedBox(width: 24.w),
            
            // 时间显示
            Consumer<AppProvider>(
              builder: (context, appProvider, child) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      appProvider.formattedTime,
                      style: TextStyle(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontFamily: 'monospace',
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      appProvider.formattedDate,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.white.withOpacity(0.8),
                      ),
                    ),
                  ],
                );
              },
            ),
            
            SizedBox(width: 24.w),
            
            // 网络状态和设置按钮
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 网络状态指示器
                Consumer<AppProvider>(
                  builder: (context, appProvider, child) {
                    return Icon(
                      appProvider.isOnline 
                          ? Icons.wifi 
                          : Icons.wifi_off,
                      color: appProvider.isOnline 
                          ? Colors.white 
                          : Colors.red[300],
                      size: 20.sp,
                    );
                  },
                ),
                
                SizedBox(width: 16.w),
                
                // 设置按钮
                IconButton(
                  onPressed: () => context.go('/settings'),
                  icon: Icon(
                    Icons.settings,
                    color: Colors.white,
                    size: 24.sp,
                  ),
                  tooltip: '设置',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
