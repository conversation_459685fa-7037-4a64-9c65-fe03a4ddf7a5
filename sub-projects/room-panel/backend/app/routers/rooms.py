from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from datetime import datetime, date

from app.database import get_db
from app.models import Room as RoomModel, Meeting as MeetingModel
from app.schemas import Room, RoomCreate, RoomUpdate, RoomWithMeetings
from app.routers.auth import get_current_active_user

router = APIRouter()

@router.post("/", response_model=Room)
async def create_room(
    room: RoomCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """创建会议室"""
    db_room = RoomModel(**room.dict())
    db.add(db_room)
    db.commit()
    db.refresh(db_room)
    return db_room

@router.get("/", response_model=List[Room])
async def read_rooms(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取会议室列表"""
    rooms = db.query(RoomModel).offset(skip).limit(limit).all()
    return rooms

@router.get("/{room_id}", response_model=RoomWithMeetings)
async def read_room(room_id: int, db: Session = Depends(get_db)):
    """获取单个会议室详情"""
    room = db.query(RoomModel).filter(RoomModel.id == room_id).first()
    if room is None:
        raise HTTPException(status_code=404, detail="Room not found")
    return room

@router.put("/{room_id}", response_model=Room)
async def update_room(
    room_id: int,
    room_update: RoomUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """更新会议室信息"""
    room = db.query(RoomModel).filter(RoomModel.id == room_id).first()
    if room is None:
        raise HTTPException(status_code=404, detail="Room not found")
    
    update_data = room_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(room, field, value)
    
    db.commit()
    db.refresh(room)
    return room

@router.delete("/{room_id}")
async def delete_room(
    room_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """删除会议室"""
    room = db.query(RoomModel).filter(RoomModel.id == room_id).first()
    if room is None:
        raise HTTPException(status_code=404, detail="Room not found")
    
    db.delete(room)
    db.commit()
    return {"message": "Room deleted successfully"}

@router.get("/{room_id}/meetings/today")
async def get_room_meetings_today(room_id: int, db: Session = Depends(get_db)):
    """获取会议室今日会议"""
    today = date.today()
    meetings = db.query(MeetingModel).filter(
        MeetingModel.room_id == room_id,
        MeetingModel.start_time >= today,
        MeetingModel.start_time < today.replace(day=today.day + 1)
    ).all()
    return meetings

@router.get("/{room_id}/status")
async def get_room_status(room_id: int, db: Session = Depends(get_db)):
    """获取会议室当前状态"""
    room = db.query(RoomModel).filter(RoomModel.id == room_id).first()
    if room is None:
        raise HTTPException(status_code=404, detail="Room not found")
    
    now = datetime.now()
    current_meeting = db.query(MeetingModel).filter(
        MeetingModel.room_id == room_id,
        MeetingModel.start_time <= now,
        MeetingModel.end_time >= now,
        MeetingModel.status == "ongoing"
    ).first()
    
    next_meeting = db.query(MeetingModel).filter(
        MeetingModel.room_id == room_id,
        MeetingModel.start_time > now,
        MeetingModel.status == "scheduled"
    ).order_by(MeetingModel.start_time).first()
    
    return {
        "room": room,
        "current_meeting": current_meeting,
        "next_meeting": next_meeting,
        "is_available": current_meeting is None
    }
