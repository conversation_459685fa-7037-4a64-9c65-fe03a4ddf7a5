from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
import qrcode
import io
import json
from datetime import datetime

from app.database import get_db
from app.models import Room as RoomModel, Meeting as MeetingModel, CheckIn as CheckInModel
from app.schemas import CheckIn, CheckInCreate
from app.routers.auth import get_current_active_user

router = APIRouter()

@router.get("/room/{room_id}")
async def generate_room_qrcode(room_id: int, db: Session = Depends(get_db)):
    """生成会议室二维码"""
    room = db.query(RoomModel).filter(RoomModel.id == room_id).first()
    if not room:
        raise HTTPException(status_code=404, detail="Room not found")
    
    # 生成二维码数据
    qr_data = {
        "type": "room_checkin",
        "room_id": room_id,
        "room_name": room.name,
        "timestamp": datetime.now().isoformat()
    }
    
    # 创建二维码
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data(json.dumps(qr_data))
    qr.make(fit=True)
    
    # 生成图片
    img = qr.make_image(fill_color="black", back_color="white")
    
    # 转换为字节流
    img_buffer = io.BytesIO()
    img.save(img_buffer, format='PNG')
    img_buffer.seek(0)
    
    return StreamingResponse(
        io.BytesIO(img_buffer.read()),
        media_type="image/png",
        headers={"Content-Disposition": f"inline; filename=room_{room_id}_qrcode.png"}
    )

@router.get("/meeting/{meeting_id}")
async def generate_meeting_qrcode(meeting_id: int, db: Session = Depends(get_db)):
    """生成会议二维码"""
    meeting = db.query(MeetingModel).filter(MeetingModel.id == meeting_id).first()
    if not meeting:
        raise HTTPException(status_code=404, detail="Meeting not found")
    
    # 生成二维码数据
    qr_data = {
        "type": "meeting_checkin",
        "meeting_id": meeting_id,
        "room_id": meeting.room_id,
        "meeting_title": meeting.title,
        "timestamp": datetime.now().isoformat()
    }
    
    # 创建二维码
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data(json.dumps(qr_data))
    qr.make(fit=True)
    
    # 生成图片
    img = qr.make_image(fill_color="black", back_color="white")
    
    # 转换为字节流
    img_buffer = io.BytesIO()
    img.save(img_buffer, format='PNG')
    img_buffer.seek(0)
    
    return StreamingResponse(
        io.BytesIO(img_buffer.read()),
        media_type="image/png",
        headers={"Content-Disposition": f"inline; filename=meeting_{meeting_id}_qrcode.png"}
    )

@router.post("/checkin", response_model=CheckIn)
async def checkin_by_qrcode(
    checkin_data: CheckInCreate,
    db: Session = Depends(get_db)
):
    """通过二维码签到"""
    # 检查会议是否存在
    meeting = db.query(MeetingModel).filter(MeetingModel.id == checkin_data.meeting_id).first()
    if not meeting:
        raise HTTPException(status_code=404, detail="Meeting not found")
    
    # 检查是否已经签到
    existing_checkin = db.query(CheckInModel).filter(
        CheckInModel.meeting_id == checkin_data.meeting_id,
        CheckInModel.user_name == checkin_data.user_name
    ).first()
    
    if existing_checkin:
        raise HTTPException(status_code=400, detail="User already checked in")
    
    # 创建签到记录
    db_checkin = CheckInModel(**checkin_data.dict())
    db.add(db_checkin)
    db.commit()
    db.refresh(db_checkin)
    
    return db_checkin

@router.get("/checkin/meeting/{meeting_id}")
async def get_meeting_checkins(meeting_id: int, db: Session = Depends(get_db)):
    """获取会议签到记录"""
    meeting = db.query(MeetingModel).filter(MeetingModel.id == meeting_id).first()
    if not meeting:
        raise HTTPException(status_code=404, detail="Meeting not found")
    
    checkins = db.query(CheckInModel).filter(CheckInModel.meeting_id == meeting_id).all()
    return {
        "meeting": meeting,
        "checkins": checkins,
        "total_checkins": len(checkins)
    }
