import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../providers/room_provider.dart';
import '../utils/constants.dart';

class QrCodeSection extends StatelessWidget {
  const QrCodeSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<RoomProvider>(
      builder: (context, roomProvider, child) {
        return Padding(
          padding: EdgeInsets.all(24.w),
          child: Column(
            children: [
              // 二维码区域
              Expanded(
                flex: 2,
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 150.w,
                        height: 150.w,
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Icon(
                          Icons.qr_code,
                          size: 80.sp,
                          color: Colors.grey[400],
                        ),
                      ),
                      SizedBox(height: 16.h),
                      Text(
                        '扫码签到',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        '使用手机扫描二维码\n快速签到参会',
                        style: Theme.of(context).textTheme.bodyMedium,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
              
              SizedBox(height: 24.h),
              
              // 操作按钮区域
              Expanded(
                flex: 1,
                child: Column(
                  children: [
                    // 扫码签到按钮
                    SizedBox(
                      width: double.infinity,
                      height: 48.h,
                      child: ElevatedButton.icon(
                        onPressed: () => context.go('/qr-scanner'),
                        icon: Icon(Icons.qr_code_scanner, size: 20.sp),
                        label: const Text('扫码签到'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(AppConstants.primaryColor),
                        ),
                      ),
                    ),
                    
                    SizedBox(height: 12.h),
                    
                    // 会议详情按钮
                    if (roomProvider.currentMeeting != null)
                      SizedBox(
                        width: double.infinity,
                        height: 48.h,
                        child: OutlinedButton.icon(
                          onPressed: () => context.go('/meeting/${roomProvider.currentMeeting!.id}'),
                          icon: Icon(Icons.info_outline, size: 20.sp),
                          label: const Text('会议详情'),
                        ),
                      ),
                    
                    const Spacer(),
                    
                    // 刷新按钮
                    IconButton(
                      onPressed: () => roomProvider.refreshRoomStatus(),
                      icon: Icon(
                        Icons.refresh,
                        size: 24.sp,
                        color: Theme.of(context).primaryColor,
                      ),
                      tooltip: '刷新',
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
