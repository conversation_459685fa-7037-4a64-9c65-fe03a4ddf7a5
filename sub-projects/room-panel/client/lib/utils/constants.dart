class AppConstants {
  // API配置
  static const String baseUrl = 'http://localhost:8000';
  static const String apiVersion = '/api/v1';
  static const String apiBaseUrl = '$baseUrl$apiVersion';
  
  // 接口端点
  static const String authEndpoint = '/auth';
  static const String roomsEndpoint = '/rooms';
  static const String meetingsEndpoint = '/meetings';
  static const String qrcodeEndpoint = '/qrcode';
  
  // 本地存储键
  static const String tokenKey = 'access_token';
  static const String userKey = 'user_info';
  static const String roomIdKey = 'room_id';
  static const String themeKey = 'theme_mode';
  
  // 屏幕尺寸（8寸平板横屏）
  static const double screenWidth = 1280.0;
  static const double screenHeight = 800.0;
  
  // 布局常量
  static const double headerHeight = 80.0;
  static const double sidebarWidth = 300.0;
  static const double cardPadding = 16.0;
  static const double borderRadius = 12.0;
  
  // 颜色
  static const int primaryColor = 0xFF2196F3;
  static const int accentColor = 0xFF03DAC6;
  static const int errorColor = 0xFFB00020;
  static const int successColor = 0xFF4CAF50;
  static const int warningColor = 0xFFFF9800;
  
  // 字体大小
  static const double titleFontSize = 24.0;
  static const double subtitleFontSize = 18.0;
  static const double bodyFontSize = 16.0;
  static const double captionFontSize = 14.0;
  
  // 动画时长
  static const int animationDuration = 300;
  static const int longAnimationDuration = 500;
  
  // 刷新间隔（毫秒）
  static const int refreshInterval = 30000; // 30秒
  static const int clockUpdateInterval = 1000; // 1秒
  
  // 会议状态
  static const String meetingStatusScheduled = 'scheduled';
  static const String meetingStatusOngoing = 'ongoing';
  static const String meetingStatusCompleted = 'completed';
  static const String meetingStatusCancelled = 'cancelled';
  
  // 会议室状态
  static const String roomStatusAvailable = 'available';
  static const String roomStatusOccupied = 'occupied';
  static const String roomStatusMaintenance = 'maintenance';
  
  // 签到方式
  static const String checkinMethodQrcode = 'qrcode';
  static const String checkinMethodManual = 'manual';
  
  // 错误消息
  static const String networkError = '网络连接失败，请检查网络设置';
  static const String serverError = '服务器错误，请稍后重试';
  static const String authError = '认证失败，请重新登录';
  static const String notFoundError = '请求的资源不存在';
  static const String unknownError = '未知错误，请联系管理员';
  
  // 成功消息
  static const String checkinSuccess = '签到成功';
  static const String updateSuccess = '更新成功';
  static const String deleteSuccess = '删除成功';
  
  // 提示消息
  static const String noMeetingsToday = '今日暂无会议安排';
  static const String roomAvailable = '会议室空闲';
  static const String scanQrcode = '请扫描二维码签到';
  static const String meetingStartingSoon = '会议即将开始';
  static const String meetingInProgress = '会议进行中';
}
