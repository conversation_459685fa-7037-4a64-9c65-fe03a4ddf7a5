import 'package:json_annotation/json_annotation.dart';

part 'room.g.dart';

@JsonSerializable()
class Room {
  final int id;
  final String name;
  final String? location;
  final int? capacity;
  final String? equipment;
  final String status;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'qr_code')
  final String? qrCode;
  @Json<PERSON>ey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;

  Room({
    required this.id,
    required this.name,
    this.location,
    this.capacity,
    this.equipment,
    required this.status,
    this.qrCode,
    required this.createdAt,
    this.updatedAt,
  });

  factory Room.fromJson(Map<String, dynamic> json) => _$RoomFromJson(json);
  Map<String, dynamic> toJson() => _$RoomToJson(this);

  // 获取状态显示文本
  String get statusText {
    switch (status) {
      case 'available':
        return '空闲';
      case 'occupied':
        return '使用中';
      case 'maintenance':
        return '维护中';
      default:
        return '未知';
    }
  }

  // 获取状态颜色
  int get statusColor {
    switch (status) {
      case 'available':
        return 0xFF4CAF50; // 绿色
      case 'occupied':
        return 0xFFFF5722; // 红色
      case 'maintenance':
        return 0xFFFF9800; // 橙色
      default:
        return 0xFF9E9E9E; // 灰色
    }
  }

  // 是否可用
  bool get isAvailable => status == 'available';

  // 复制对象
  Room copyWith({
    int? id,
    String? name,
    String? location,
    int? capacity,
    String? equipment,
    String? status,
    String? qrCode,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Room(
      id: id ?? this.id,
      name: name ?? this.name,
      location: location ?? this.location,
      capacity: capacity ?? this.capacity,
      equipment: equipment ?? this.equipment,
      status: status ?? this.status,
      qrCode: qrCode ?? this.qrCode,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Room{id: $id, name: $name, status: $status}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Room && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}
