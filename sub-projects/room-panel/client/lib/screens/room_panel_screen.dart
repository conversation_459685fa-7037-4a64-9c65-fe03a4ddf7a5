import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../providers/app_provider.dart';
import '../providers/room_provider.dart';
import '../widgets/room_header.dart';
import '../widgets/current_meeting_card.dart';
import '../widgets/next_meeting_card.dart';
import '../widgets/today_meetings_list.dart';
import '../widgets/qr_code_section.dart';
import '../utils/constants.dart';

class RoomPanelScreen extends StatefulWidget {
  final String roomId;

  const RoomPanelScreen({
    super.key,
    required this.roomId,
  });

  @override
  State<RoomPanelScreen> createState() => _RoomPanelScreenState();
}

class _RoomPanelScreenState extends State<RoomPanelScreen> {
  late Timer _refreshTimer;

  @override
  void initState() {
    super.initState();
    _initializeRoom();
    _startAutoRefresh();
  }

  void _initializeRoom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AppProvider>().setCurrentRoomId(widget.roomId);
      context.read<RoomProvider>().loadRoomInfo(widget.roomId);
    });
  }

  void _startAutoRefresh() {
    _refreshTimer = Timer.periodic(
      const Duration(milliseconds: AppConstants.refreshInterval),
      (timer) {
        if (mounted) {
          context.read<RoomProvider>().refreshRoomStatus();
        }
      },
    );
  }

  @override
  void dispose() {
    _refreshTimer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<RoomProvider>(
        builder: (context, roomProvider, child) {
          if (roomProvider.isLoading && roomProvider.currentRoom == null) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (roomProvider.error != null && roomProvider.currentRoom == null) {
            return _buildErrorView(roomProvider.error!);
          }

          return _buildMainContent(roomProvider);
        },
      ),
    );
  }

  Widget _buildErrorView(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64.sp,
            color: Colors.red,
          ),
          SizedBox(height: 16.h),
          Text(
            '加载失败',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          SizedBox(height: 8.h),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton(
                onPressed: () => _initializeRoom(),
                child: const Text('重试'),
              ),
              SizedBox(width: 16.w),
              OutlinedButton(
                onPressed: () => context.go('/settings'),
                child: const Text('设置'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent(RoomProvider roomProvider) {
    return Column(
      children: [
        // 顶部标题栏
        const RoomHeader(),
        
        // 主要内容区域
        Expanded(
          child: Row(
            children: [
              // 左侧主要信息区域
              Expanded(
                flex: 2,
                child: Padding(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    children: [
                      // 当前会议卡片
                      const CurrentMeetingCard(),
                      
                      SizedBox(height: 16.h),
                      
                      // 下一个会议卡片
                      const NextMeetingCard(),
                      
                      SizedBox(height: 16.h),
                      
                      // 今日会议列表
                      const Expanded(
                        child: TodayMeetingsList(),
                      ),
                    ],
                  ),
                ),
              ),
              
              // 右侧二维码和操作区域
              Container(
                width: AppConstants.sidebarWidth.w,
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(-2, 0),
                    ),
                  ],
                ),
                child: const QrCodeSection(),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
