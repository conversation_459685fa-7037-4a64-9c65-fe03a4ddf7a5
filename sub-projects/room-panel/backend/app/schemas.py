from pydantic import BaseModel, EmailStr
from datetime import datetime
from typing import Optional, List
from enum import Enum

# 枚举类型
class RoomStatus(str, Enum):
    available = "available"
    occupied = "occupied"
    maintenance = "maintenance"

class MeetingStatus(str, Enum):
    scheduled = "scheduled"
    ongoing = "ongoing"
    completed = "completed"
    cancelled = "cancelled"

class CheckInMethod(str, Enum):
    qrcode = "qrcode"
    manual = "manual"

# 基础模式
class RoomBase(BaseModel):
    name: str
    location: Optional[str] = None
    capacity: Optional[int] = None
    equipment: Optional[str] = None
    status: RoomStatus = RoomStatus.available

class RoomCreate(RoomBase):
    pass

class RoomUpdate(BaseModel):
    name: Optional[str] = None
    location: Optional[str] = None
    capacity: Optional[int] = None
    equipment: Optional[str] = None
    status: Optional[RoomStatus] = None

class Room(RoomBase):
    id: int
    qr_code: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

# 会议相关模式
class MeetingBase(BaseModel):
    room_id: int
    title: str
    organizer: Optional[str] = None
    start_time: datetime
    end_time: datetime
    attendees: Optional[str] = None
    status: MeetingStatus = MeetingStatus.scheduled

class MeetingCreate(MeetingBase):
    pass

class MeetingUpdate(BaseModel):
    title: Optional[str] = None
    organizer: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    attendees: Optional[str] = None
    status: Optional[MeetingStatus] = None

class Meeting(MeetingBase):
    id: int
    wework_meeting_id: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    room: Optional[Room] = None
    
    class Config:
        from_attributes = True

# 签到相关模式
class CheckInBase(BaseModel):
    meeting_id: int
    user_name: str
    user_id: Optional[str] = None
    checkin_method: CheckInMethod = CheckInMethod.qrcode

class CheckInCreate(CheckInBase):
    pass

class CheckIn(CheckInBase):
    id: int
    checkin_time: datetime
    meeting: Optional[Meeting] = None
    
    class Config:
        from_attributes = True

# 用户相关模式
class UserBase(BaseModel):
    username: str
    email: Optional[EmailStr] = None
    is_active: bool = True
    is_admin: bool = False

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    is_active: Optional[bool] = None
    is_admin: Optional[bool] = None
    password: Optional[str] = None

class User(UserBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

# 认证相关模式
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

# 响应模式
class RoomWithMeetings(Room):
    meetings: List[Meeting] = []

class MeetingWithCheckIns(Meeting):
    checkins: List[CheckIn] = []
