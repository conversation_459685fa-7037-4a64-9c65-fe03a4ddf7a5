#!/bin/bash

# 智能门牌系统后端启动脚本

echo "正在启动智能门牌系统后端..."

# 检查是否存在虚拟环境
if [ ! -d "venv" ]; then
    echo "创建虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
echo "激活虚拟环境..."
source venv/bin/activate

# 安装依赖
echo "安装依赖..."
pip install -r requirements.txt

# 检查环境变量文件
if [ ! -f ".env" ]; then
    echo "复制环境变量文件..."
    cp .env.example .env
    echo "请编辑 .env 文件配置数据库和企业微信API信息"
fi

# 启动应用
echo "启动应用..."
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
