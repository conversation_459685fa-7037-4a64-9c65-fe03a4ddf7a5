智能门牌项目，可通过门牌查看会议室预定和使用情况，支持扫码签到

## 项目结构

1. **后端** (backend/) - 对接企业微信会议API，用python, fastapi, dotenv, sqlalchemy orm, postgresql
2. **客户端** (client/) - 用flutter，支持安卓，适配安卓8寸平板
3. **前端页面** (frontend/) - 用shadcn/ui
4. **文档** (docs/) - 项目文档
5. **测试** - backend/test 和 client/test
6. **智能门牌支持多模板**

## 当前进展

### ✅ 后端 (已完成基础架构)
- FastAPI 应用框架搭建
- PostgreSQL 数据库模型设计
- 企业微信会议API集成服务
- JWT 认证系统
- 会议室管理API
- 会议管理API
- 二维码生成和签到API
- Docker 容器化配置

### ✅ 客户端 (已完成基础架构)
- Flutter 应用框架搭建
- 8寸平板横屏适配
- Provider 状态管理
- 会议室状态显示
- 会议信息展示界面
- 二维码签到功能
- 主题切换支持
- 自动刷新机制
- 网络状态监控

### ✅ 前端管理页面 (已完成基础架构)
- Next.js 14 + shadcn/ui 框架搭建
- 响应式管理后台界面
- 仪表板数据概览
- 会议室管理功能
- 会议管理界面
- 签到记录查看
- 用户权限管理
- 统计分析图表
- 明暗主题切换
- API集成和状态管理

### 🔄 待完善
- 测试用例编写
- 真机二维码扫描功能
- 部署配置优化
- 更多管理功能细化

## 快速开始

### 后端启动
```bash
cd backend
./start.sh
```

访问 API 文档: http://localhost:8000/docs

### 客户端开发
```bash
cd client
flutter pub get
flutter run
```

### 客户端构建
```bash
cd client
./build.sh
```

### 前端启动
```bash
cd frontend
./start.sh
```

访问管理后台: http://localhost:3000

## 功能特性

### 后端功能
- 🏢 会议室管理 (增删改查)
- 📅 会议管理 (创建、更新、取消)
- 🔗 企业微信会议API集成
- 📱 二维码生成和扫码签到
- 👤 用户认证和权限管理
- 📊 会议统计和报表
- 🔄 实时状态更新

### 客户端功能
- 🏢 会议室状态实时显示
- 📅 当前会议和今日会议展示
- 📱 二维码扫码签到
- 🎨 8寸平板横屏适配
- 🌙 明暗主题切换
- 🔄 自动刷新机制
- 📶 网络状态监控
- ⚙️ 设置和配置管理

### 前端管理功能
- 🏢 会议室信息管理和状态监控
- 📅 会议创建、编辑、状态跟踪
- 👥 签到记录查看和统计分析
- 📊 使用率统计和趋势分析
- 👤 用户权限和账户管理
- ⚙️ 系统配置和参数设置
- 🌙 响应式设计和主题切换
- 📱 桌面和移动端适配
