'use client'

import { useState } from 'react'
import { useQuery } from 'react-query'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  MapPin, 
  Users, 
  Settings,
  Plus,
  Search,
  Filter
} from 'lucide-react'
import Link from 'next/link'
import { getRooms } from '@/lib/api'
import { getRoomStatusText, getRoomStatusColor } from '@/lib/utils'

export default function RoomsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  const { data: rooms, isLoading, error } = useQuery('rooms', getRooms)

  const filteredRooms = rooms?.filter((room: any) => {
    const matchesSearch = room.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         room.location?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || room.status === statusFilter
    return matchesSearch && matchesStatus
  }) || []

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">会议室管理</h1>
            <p className="text-gray-600">管理所有会议室信息和状态</p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">加载会议室数据失败</p>
        <Button className="mt-4" onClick={() => window.location.reload()}>
          重试
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">会议室管理</h1>
          <p className="text-gray-600">管理所有会议室信息和状态</p>
        </div>
        <Button asChild>
          <Link href="/dashboard/rooms/new">
            <Plus className="mr-2 h-4 w-4" />
            添加会议室
          </Link>
        </Button>
      </div>

      {/* 搜索和筛选 */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="搜索会议室名称或位置..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4 text-gray-400" />
          <select
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="all">全部状态</option>
            <option value="available">空闲</option>
            <option value="occupied">使用中</option>
            <option value="maintenance">维护中</option>
          </select>
        </div>
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{rooms?.length || 0}</div>
            <p className="text-sm text-gray-600">总会议室</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">
              {rooms?.filter((r: any) => r.status === 'available').length || 0}
            </div>
            <p className="text-sm text-gray-600">空闲</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">
              {rooms?.filter((r: any) => r.status === 'occupied').length || 0}
            </div>
            <p className="text-sm text-gray-600">使用中</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-yellow-600">
              {rooms?.filter((r: any) => r.status === 'maintenance').length || 0}
            </div>
            <p className="text-sm text-gray-600">维护中</p>
          </CardContent>
        </Card>
      </div>

      {/* 会议室列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredRooms.map((room: any) => (
          <Card key={room.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{room.name}</CardTitle>
                  <CardDescription className="flex items-center mt-1">
                    <MapPin className="h-4 w-4 mr-1" />
                    {room.location || '未设置位置'}
                  </CardDescription>
                </div>
                <Badge className={getRoomStatusColor(room.status)}>
                  {getRoomStatusText(room.status)}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {room.capacity && (
                  <div className="flex items-center text-sm text-gray-600">
                    <Users className="h-4 w-4 mr-2" />
                    容纳 {room.capacity} 人
                  </div>
                )}
                {room.equipment && (
                  <div className="text-sm text-gray-600">
                    <strong>设备:</strong> {room.equipment}
                  </div>
                )}
              </div>
              <div className="flex justify-between items-center mt-4">
                <Button variant="outline" size="sm" asChild>
                  <Link href={`/dashboard/rooms/${room.id}`}>
                    查看详情
                  </Link>
                </Button>
                <Button variant="ghost" size="sm" asChild>
                  <Link href={`/dashboard/rooms/${room.id}/edit`}>
                    <Settings className="h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredRooms.length === 0 && (
        <div className="text-center py-12">
          <MapPin className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">没有找到会议室</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm || statusFilter !== 'all' 
              ? '尝试调整搜索条件或筛选器' 
              : '开始添加第一个会议室'}
          </p>
          {!searchTerm && statusFilter === 'all' && (
            <div className="mt-6">
              <Button asChild>
                <Link href="/dashboard/rooms/new">
                  <Plus className="mr-2 h-4 w-4" />
                  添加会议室
                </Link>
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
