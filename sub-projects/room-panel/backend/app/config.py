import os
from pydantic_settings import BaseSettings
from typing import Optional

#用dotenv支持多环境
import dotenv

class Settings(BaseSettings):
    # 数据库配置
    DATABASE_URL: str = "postgresql://username:password@localhost:5432/room_panel"
    
    # 企业微信会议API配置
    WEWORK_CORP_ID: str = ""
    WEWORK_CORP_SECRET: str = ""
    WEWORK_AGENT_ID: str = ""
    WEWORK_MEETING_SECRET: str = ""
    
    # JWT配置
    SECRET_KEY: str = "your_secret_key_here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 应用配置
    DEBUG: bool = True
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 企业微信API基础URL
    WEWORK_API_BASE_URL: str = "https://qyapi.weixin.qq.com"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
