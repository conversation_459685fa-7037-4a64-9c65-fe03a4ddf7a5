// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'meeting.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Meeting _$MeetingFromJson(Map<String, dynamic> json) => Meeting(
      id: (json['id'] as num).toInt(),
      roomId: (json['room_id'] as num).toInt(),
      title: json['title'] as String,
      organizer: json['organizer'] as String?,
      startTime: DateTime.parse(json['start_time'] as String),
      endTime: DateTime.parse(json['end_time'] as String),
      attendees: json['attendees'] as String?,
      status: json['status'] as String,
      weworkMeetingId: json['wework_meeting_id'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      room: json['room'] == null
          ? null
          : Room.fromJson(json['room'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$MeetingToJson(Meeting instance) => <String, dynamic>{
      'id': instance.id,
      'room_id': instance.roomId,
      'title': instance.title,
      'organizer': instance.organizer,
      'start_time': instance.startTime.toIso8601String(),
      'end_time': instance.endTime.toIso8601String(),
      'attendees': instance.attendees,
      'status': instance.status,
      'wework_meeting_id': instance.weworkMeetingId,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'room': instance.room,
    };
