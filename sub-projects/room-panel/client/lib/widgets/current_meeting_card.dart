import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../providers/room_provider.dart';
import '../utils/constants.dart';

class CurrentMeetingCard extends StatelessWidget {
  const CurrentMeetingCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<RoomProvider>(
      builder: (context, roomProvider, child) {
        final currentMeeting = roomProvider.currentMeeting;
        
        if (currentMeeting == null) {
          return _buildEmptyCard(context);
        }
        
        return _buildMeetingCard(context, currentMeeting, roomProvider);
      },
    );
  }

  Widget _buildEmptyCard(BuildContext context) {
    return Card(
      child: Container(
        height: 200.h,
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.event_available,
              size: 48.sp,
              color: const Color(AppConstants.successColor),
            ),
            SizedBox(height: 16.h),
            Text(
              '会议室空闲',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: const Color(AppConstants.successColor),
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              '当前没有进行中的会议',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMeetingCard(BuildContext context, meeting, RoomProvider roomProvider) {
    return Card(
      child: InkWell(
        onTap: () => context.go('/meeting/${meeting.id}'),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Container(
          height: 200.h,
          padding: EdgeInsets.all(24.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题和状态
              Row(
                children: [
                  Expanded(
                    child: Text(
                      meeting.title,
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                    decoration: BoxDecoration(
                      color: Color(meeting.statusColor),
                      borderRadius: BorderRadius.circular(16.r),
                    ),
                    child: Text(
                      meeting.statusText,
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              
              SizedBox(height: 16.h),
              
              // 时间信息
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16.sp,
                    color: Theme.of(context).iconTheme.color,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    meeting.timeRange,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Text(
                    '(${meeting.durationInMinutes}分钟)',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
              
              SizedBox(height: 12.h),
              
              // 组织者信息
              if (meeting.organizer != null) ...[
                Row(
                  children: [
                    Icon(
                      Icons.person,
                      size: 16.sp,
                      color: Theme.of(context).iconTheme.color,
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      '组织者: ${meeting.organizer}',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
                SizedBox(height: 12.h),
              ],
              
              // 参会人员
              if (meeting.attendeesList.isNotEmpty) ...[
                Row(
                  children: [
                    Icon(
                      Icons.group,
                      size: 16.sp,
                      color: Theme.of(context).iconTheme.color,
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Text(
                        '参会人员: ${meeting.attendeesList.take(3).join(', ')}${meeting.attendeesList.length > 3 ? ' 等${meeting.attendeesList.length}人' : ''}',
                        style: Theme.of(context).textTheme.bodyMedium,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
              
              const Spacer(),
              
              // 操作按钮
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton.icon(
                    onPressed: () => context.go('/meeting/${meeting.id}'),
                    icon: Icon(Icons.info_outline, size: 16.sp),
                    label: const Text('查看详情'),
                  ),
                  SizedBox(width: 8.w),
                  ElevatedButton.icon(
                    onPressed: () => context.go('/qr-scanner'),
                    icon: Icon(Icons.qr_code_scanner, size: 16.sp),
                    label: const Text('扫码签到'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
