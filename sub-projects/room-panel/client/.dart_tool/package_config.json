{"configVersion": 2, "packages": [{"name": "_fe_analyzer_shared", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/_fe_analyzer_shared-64.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "analyzer", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/analyzer-6.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "args", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/args-2.5.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/async-2.11.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "boolean_selector", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/boolean_selector-2.1.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "build", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/build-2.4.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "build_config", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/build_config-1.1.1", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "build_daemon", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/build_daemon-4.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "build_resolvers", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/build_resolvers-2.4.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "build_runner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/build_runner-2.4.9", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "build_runner_core", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/build_runner_core-7.3.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "built_collection", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/built_collection-5.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "built_value", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/built_value-8.11.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "characters", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/characters-1.3.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "checked_yaml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/checked_yaml-2.0.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "clock", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/clock-1.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "code_builder", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/code_builder-4.10.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "collection", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.18.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "connectivity_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-5.0.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "connectivity_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus_platform_interface-1.2.4", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "convert", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/convert-3.1.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "crypto", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/crypto-3.0.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "cupertino_icons", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "dart_style", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/dart_style-2.3.6", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "dbus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/dbus-0.7.11", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "device_info_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-9.1.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "device_info_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus_platform_interface-7.0.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "dio", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/dio-5.8.0+1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "dio_web_adapter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/dio_web_adapter-1.1.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "fake_async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/fake_async-1.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "ffi", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/ffi-2.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "fixnum", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/fixnum-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter", "rootUri": "file:///Users/<USER>/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "flutter_lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_lints-3.0.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter_screenutil", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_screenutil-5.9.3", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_test", "rootUri": "file:///Users/<USER>/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "flutter_web_plugins", "rootUri": "file:///Users/<USER>/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "frontend_server_client", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/frontend_server_client-4.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "glob", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/glob-2.1.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "go_router", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/go_router-12.1.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "graphs", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/graphs-2.3.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "http", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/http-1.2.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http_multi_server", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/http_multi_server-3.2.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http_parser", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/http_parser-4.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "intl", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/intl-0.18.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "io", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/io-1.0.4", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "js", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/js-0.6.7", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "json_annotation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/json_annotation-4.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "json_serializable", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/json_serializable-6.8.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/lints-3.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "logger", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/logger-2.6.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "logging", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/logging-1.2.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "matcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/matcher-0.12.16", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "material_color_utilities", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/material_color_utilities-0.5.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "material_design_icons_flutter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/material_design_icons_flutter-7.0.7296", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "meta", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/meta-1.10.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mime", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/mime-1.0.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "nested", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/nested-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "nm", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/nm-0.5.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "package_config", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/package_config-2.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path-1.8.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path_provider_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "permission_handler", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/permission_handler-11.3.1", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "permission_handler_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_android-12.0.13", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "permission_handler_apple", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "permission_handler_html", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_html-0.1.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "permission_handler_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_platform_interface-4.2.3", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "permission_handler_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_windows-0.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "petitparser", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/petitparser-6.0.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "platform", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pool", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/pool-1.5.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "provider", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/provider-6.1.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "pub_semver", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/pub_semver-2.1.4", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "pubspec_parse", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/pubspec_parse-1.4.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "qr", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/qr-3.0.1", "packageUri": "lib/", "languageVersion": "2.16"}, {"name": "qr_code_scanner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/qr_code_scanner-1.0.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "qr_flutter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/qr_flutter-4.1.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "shared_preferences", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences-2.2.3", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "shared_preferences_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_android-2.2.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_foundation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.3.5", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "shared_preferences_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_linux-2.4.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_platform_interface-2.4.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_web-2.2.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_windows-2.4.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shelf", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/shelf-1.4.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "shelf_web_socket", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/shelf_web_socket-1.0.4", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "sky_engine", "rootUri": "file:///Users/<USER>/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "source_gen", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/source_gen-1.5.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "source_helper", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/source_helper-1.3.4", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "source_span", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/source_span-1.10.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "stack_trace", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/stack_trace-1.11.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "stream_channel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/stream_channel-2.1.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "stream_transform", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/stream_transform-2.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "string_scanner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/string_scanner-1.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "term_glyph", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/term_glyph-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "test_api", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/test_api-0.6.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "timing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/timing-1.0.1", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "typed_data", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/typed_data-1.3.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "vector_math", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "watcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/watcher-1.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/web-0.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "web_socket_channel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/web_socket_channel-2.4.0", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "win32", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/win32-5.2.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "win32_registry", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/win32_registry-1.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "xdg_directories", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/xdg_directories-1.0.4", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "xml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "yaml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/yaml-3.1.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "room_panel_client", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.0"}], "generated": "2025-08-04T07:48:32.313642Z", "generator": "pub", "generatorVersion": "3.2.3"}