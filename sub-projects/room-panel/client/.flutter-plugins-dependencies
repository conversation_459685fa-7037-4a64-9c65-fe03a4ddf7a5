{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-5.0.2/", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-9.1.2/", "native_build": true, "dependencies": []}, {"name": "permission_handler_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/", "native_build": true, "dependencies": []}, {"name": "qr_code_scanner", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/qr_code_scanner-1.0.1/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.3.5/", "shared_darwin_source": true, "native_build": true, "dependencies": []}], "android": [{"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-5.0.2/", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-9.1.2/", "native_build": true, "dependencies": []}, {"name": "permission_handler_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_android-12.0.13/", "native_build": true, "dependencies": []}, {"name": "qr_code_scanner", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/qr_code_scanner-1.0.1/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_android-2.2.2/", "native_build": true, "dependencies": []}], "macos": [{"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-5.0.2/", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-9.1.2/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.3.5/", "shared_darwin_source": true, "native_build": true, "dependencies": []}], "linux": [{"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-5.0.2/", "native_build": false, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-9.1.2/", "native_build": false, "dependencies": []}, {"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path_provider_linux-2.2.1/", "native_build": false, "dependencies": []}, {"name": "shared_preferences_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_linux-2.4.0/", "native_build": false, "dependencies": ["path_provider_linux"]}], "windows": [{"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-5.0.2/", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-9.1.2/", "native_build": false, "dependencies": []}, {"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path_provider_windows-2.3.0/", "native_build": false, "dependencies": []}, {"name": "permission_handler_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_windows-0.2.1/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_windows-2.4.0/", "native_build": false, "dependencies": ["path_provider_windows"]}], "web": [{"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-5.0.2/", "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-9.1.2/", "dependencies": []}, {"name": "permission_handler_html", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_html-0.1.1/", "dependencies": []}, {"name": "shared_preferences_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_web-2.2.2/", "dependencies": []}]}, "dependencyGraph": [{"name": "connectivity_plus", "dependencies": []}, {"name": "device_info_plus", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "qr_code_scanner", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}], "date_created": "2025-08-04 15:48:32.401949", "version": "3.16.5"}