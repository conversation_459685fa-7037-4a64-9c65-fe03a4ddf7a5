import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/room.dart';
import '../models/meeting.dart';
import '../models/checkin.dart';
import '../utils/constants.dart';

class ApiService {
  late final Dio _dio;
  final Logger _logger = Logger();
  String? _accessToken;

  ApiService() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConstants.apiBaseUrl,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
      headers: {
        'Content-Type': 'application/json',
      },
    ));

    // 添加拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // 添加认证头
        if (_accessToken != null) {
          options.headers['Authorization'] = 'Bearer $_accessToken';
        }
        _logger.d('Request: ${options.method} ${options.path}');
        handler.next(options);
      },
      onResponse: (response, handler) {
        _logger.d('Response: ${response.statusCode} ${response.data}');
        handler.next(response);
      },
      onError: (error, handler) {
        _logger.e('Error: ${error.message}');
        handler.next(error);
      },
    ));

    _loadToken();
  }

  // 加载保存的token
  Future<void> _loadToken() async {
    final prefs = await SharedPreferences.getInstance();
    _accessToken = prefs.getString(AppConstants.tokenKey);
  }

  // 保存token
  Future<void> _saveToken(String token) async {
    _accessToken = token;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.tokenKey, token);
  }

  // 清除token
  Future<void> _clearToken() async {
    _accessToken = null;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.tokenKey);
  }

  // 登录
  Future<Map<String, dynamic>> login(String username, String password) async {
    try {
      final response = await _dio.post(
        '${AppConstants.authEndpoint}/token',
        data: FormData.fromMap({
          'username': username,
          'password': password,
        }),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        await _saveToken(data['access_token']);
        return data;
      } else {
        throw Exception('登录失败');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // 获取会议室信息
  Future<Room> getRoom(String roomId) async {
    try {
      final response = await _dio.get('${AppConstants.roomsEndpoint}/$roomId');
      return Room.fromJson(response.data);
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // 获取会议室状态
  Future<Map<String, dynamic>> getRoomStatus(String roomId) async {
    try {
      final response = await _dio.get('${AppConstants.roomsEndpoint}/$roomId/status');
      return response.data;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // 获取会议室今日会议
  Future<List<Meeting>> getRoomMeetingsToday(String roomId) async {
    try {
      final response = await _dio.get('${AppConstants.roomsEndpoint}/$roomId/meetings/today');
      final List<dynamic> data = response.data;
      return data.map((json) => Meeting.fromJson(json)).toList();
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // 获取会议详情
  Future<Meeting> getMeeting(String meetingId) async {
    try {
      final response = await _dio.get('${AppConstants.meetingsEndpoint}/$meetingId');
      return Meeting.fromJson(response.data);
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // 获取会议列表
  Future<List<Meeting>> getMeetings({
    String? roomId,
    DateTime? startDate,
    DateTime? endDate,
    int skip = 0,
    int limit = 100,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'skip': skip,
        'limit': limit,
      };

      if (roomId != null) queryParams['room_id'] = roomId;
      if (startDate != null) queryParams['start_date'] = startDate.toIso8601String().split('T')[0];
      if (endDate != null) queryParams['end_date'] = endDate.toIso8601String().split('T')[0];

      final response = await _dio.get(
        AppConstants.meetingsEndpoint,
        queryParameters: queryParams,
      );

      final List<dynamic> data = response.data;
      return data.map((json) => Meeting.fromJson(json)).toList();
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // 签到
  Future<CheckIn> checkIn(CheckInRequest request) async {
    try {
      final response = await _dio.post(
        '${AppConstants.qrcodeEndpoint}/checkin',
        data: request.toJson(),
      );
      return CheckIn.fromJson(response.data);
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // 获取会议签到记录
  Future<Map<String, dynamic>> getMeetingCheckIns(String meetingId) async {
    try {
      final response = await _dio.get('${AppConstants.qrcodeEndpoint}/checkin/meeting/$meetingId');
      return response.data;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // 处理Dio错误
  Exception _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return Exception(AppConstants.networkError);
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        switch (statusCode) {
          case 401:
            _clearToken();
            return Exception(AppConstants.authError);
          case 404:
            return Exception(AppConstants.notFoundError);
          case 500:
            return Exception(AppConstants.serverError);
          default:
            return Exception(e.response?.data['detail'] ?? AppConstants.unknownError);
        }
      default:
        return Exception(AppConstants.networkError);
    }
  }

  // 检查网络连接
  Future<bool> checkConnection() async {
    try {
      final response = await _dio.get('/health');
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}
