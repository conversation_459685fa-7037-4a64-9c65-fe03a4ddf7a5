import 'package:flutter/material.dart';
import '../models/meeting.dart';
import '../models/checkin.dart';
import '../services/api_service.dart';

class MeetingProvider extends ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  Meeting? _currentMeeting;
  List<CheckIn> _checkIns = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  Meeting? get currentMeeting => _currentMeeting;
  List<CheckIn> get checkIns => _checkIns;
  bool get isLoading => _isLoading;
  String? get error => _error;
  int get totalCheckIns => _checkIns.length;

  // 加载会议详情
  Future<void> loadMeetingDetail(String meetingId) async {
    _setLoading(true);
    _clearError();

    try {
      _currentMeeting = await _apiService.getMeeting(meetingId);
      await _loadCheckIns(meetingId);
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // 加载签到记录
  Future<void> _loadCheckIns(String meetingId) async {
    try {
      final data = await _apiService.getMeetingCheckIns(meetingId);
      final List<dynamic> checkInsData = data['checkins'] ?? [];
      _checkIns = checkInsData.map((json) => CheckIn.fromJson(json)).toList();
      notifyListeners();
    } catch (e) {
      // 签到记录加载失败不影响主流程
      _checkIns = [];
    }
  }

  // 刷新签到记录
  Future<void> refreshCheckIns() async {
    if (_currentMeeting == null) return;
    await _loadCheckIns(_currentMeeting!.id.toString());
  }

  // 执行签到
  Future<bool> checkIn(String userName, {String? userId}) async {
    if (_currentMeeting == null) return false;

    try {
      final request = CheckInRequest(
        meetingId: _currentMeeting!.id,
        userName: userName,
        userId: userId,
        checkinMethod: 'qrcode',
      );

      final checkIn = await _apiService.checkIn(request);
      _checkIns.add(checkIn);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  // 检查用户是否已签到
  bool isUserCheckedIn(String userName) {
    return _checkIns.any((checkIn) => checkIn.userName == userName);
  }

  // 获取签到率
  double get checkInRate {
    if (_currentMeeting == null) return 0.0;
    
    final attendeesList = _currentMeeting!.attendeesList;
    if (attendeesList.isEmpty) return 0.0;
    
    return _checkIns.length / attendeesList.length;
  }

  // 获取签到率文本
  String get checkInRateText {
    if (_currentMeeting == null) return '0%';
    
    final attendeesList = _currentMeeting!.attendeesList;
    if (attendeesList.isEmpty) return '${_checkIns.length}人已签到';
    
    final rate = (checkInRate * 100).toInt();
    return '$rate% (${_checkIns.length}/${attendeesList.length})';
  }

  // 获取未签到人员
  List<String> get uncheckedAttendees {
    if (_currentMeeting == null) return [];
    
    final attendeesList = _currentMeeting!.attendeesList;
    final checkedInNames = _checkIns.map((c) => c.userName).toSet();
    
    return attendeesList.where((name) => !checkedInNames.contains(name)).toList();
  }

  // 获取会议状态描述
  String get meetingStatusDescription {
    if (_currentMeeting == null) return '';
    
    final now = DateTime.now();
    
    if (_currentMeeting!.isOngoing) {
      final endTime = _currentMeeting!.endTime;
      final remainingMinutes = endTime.difference(now).inMinutes;
      if (remainingMinutes > 0) {
        return '会议进行中，还有${remainingMinutes}分钟结束';
      } else {
        return '会议已超时';
      }
    } else if (_currentMeeting!.isStartingSoon) {
      final startTime = _currentMeeting!.startTime;
      final minutesUntilStart = startTime.difference(now).inMinutes;
      return '会议将在${minutesUntilStart}分钟后开始';
    } else if (_currentMeeting!.isCompleted) {
      return '会议已结束';
    } else {
      return _currentMeeting!.statusText;
    }
  }

  // 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // 设置错误
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  // 清除错误
  void _clearError() {
    _error = null;
    notifyListeners();
  }

  // 清除数据
  void clear() {
    _currentMeeting = null;
    _checkIns = [];
    _isLoading = false;
    _error = null;
    notifyListeners();
  }
}
