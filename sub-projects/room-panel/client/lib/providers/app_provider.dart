import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/constants.dart';

class AppProvider extends ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.light;
  String? _currentRoomId;
  bool _isOnline = true;
  DateTime _currentTime = DateTime.now();

  // Getters
  ThemeMode get themeMode => _themeMode;
  String? get currentRoomId => _currentRoomId;
  bool get isOnline => _isOnline;
  DateTime get currentTime => _currentTime;

  AppProvider() {
    _loadSettings();
    _startClock();
  }

  // 加载设置
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    // 加载主题设置
    final themeIndex = prefs.getInt(AppConstants.themeKey) ?? 0;
    _themeMode = ThemeMode.values[themeIndex];
    
    // 加载当前会议室ID
    _currentRoomId = prefs.getString(AppConstants.roomIdKey);
    
    notifyListeners();
  }

  // 设置主题模式
  Future<void> setThemeMode(ThemeMode mode) async {
    _themeMode = mode;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(AppConstants.themeKey, mode.index);
    notifyListeners();
  }

  // 设置当前会议室ID
  Future<void> setCurrentRoomId(String roomId) async {
    _currentRoomId = roomId;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.roomIdKey, roomId);
    notifyListeners();
  }

  // 设置在线状态
  void setOnlineStatus(bool isOnline) {
    if (_isOnline != isOnline) {
      _isOnline = isOnline;
      notifyListeners();
    }
  }

  // 启动时钟
  void _startClock() {
    Stream.periodic(
      const Duration(milliseconds: AppConstants.clockUpdateInterval),
      (i) => DateTime.now(),
    ).listen((time) {
      _currentTime = time;
      notifyListeners();
    });
  }

  // 格式化当前时间
  String get formattedTime {
    return '${_currentTime.hour.toString().padLeft(2, '0')}:'
           '${_currentTime.minute.toString().padLeft(2, '0')}:'
           '${_currentTime.second.toString().padLeft(2, '0')}';
  }

  // 格式化当前日期
  String get formattedDate {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    final weekday = weekdays[_currentTime.weekday % 7];
    return '${_currentTime.year}年${_currentTime.month}月${_currentTime.day}日 $weekday';
  }

  // 切换主题
  void toggleTheme() {
    final newMode = _themeMode == ThemeMode.light 
        ? ThemeMode.dark 
        : ThemeMode.light;
    setThemeMode(newMode);
  }

  // 重置设置
  Future<void> resetSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
    
    _themeMode = ThemeMode.light;
    _currentRoomId = null;
    
    notifyListeners();
  }
}
