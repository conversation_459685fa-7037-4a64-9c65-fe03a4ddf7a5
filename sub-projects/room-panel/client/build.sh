#!/bin/bash

# 智能门牌客户端构建脚本

echo "开始构建智能门牌客户端..."

# 检查Flutter环境
if ! command -v flutter &> /dev/null; then
    echo "错误: Flutter未安装或未添加到PATH"
    exit 1
fi

echo "Flutter版本信息:"
flutter --version

# 清理项目
echo "清理项目..."
flutter clean

# 获取依赖
echo "获取依赖包..."
flutter pub get

# 生成代码
echo "生成代码文件..."
flutter packages pub run build_runner build --delete-conflicting-outputs

# 检查代码
echo "检查代码..."
flutter analyze

# 构建APK
echo "构建发布版APK..."
flutter build apk --release

# 检查构建结果
if [ -f "build/app/outputs/flutter-apk/app-release.apk" ]; then
    echo "✅ 构建成功!"
    echo "APK文件位置: build/app/outputs/flutter-apk/app-release.apk"
    
    # 显示APK信息
    APK_SIZE=$(du -h build/app/outputs/flutter-apk/app-release.apk | cut -f1)
    echo "APK大小: $APK_SIZE"
else
    echo "❌ 构建失败!"
    exit 1
fi

echo "构建完成!"
