// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Room _$Room<PERSON>rom<PERSON>son(Map<String, dynamic> json) => Room(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      location: json['location'] as String?,
      capacity: (json['capacity'] as num?)?.toInt(),
      equipment: json['equipment'] as String?,
      status: json['status'] as String,
      qrCode: json['qr_code'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$RoomToJson(Room instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'location': instance.location,
      'capacity': instance.capacity,
      'equipment': instance.equipment,
      'status': instance.status,
      'qr_code': instance.qrCode,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
    };
