# 智能门牌客户端

基于 Flutter 开发的智能门牌客户端应用，专为安卓8寸平板横屏显示设计。

## 功能特性

- 🏢 **会议室状态显示** - 实时显示会议室使用状态
- 📅 **会议信息展示** - 显示当前会议和今日会议安排
- 📱 **二维码签到** - 支持扫码快速签到
- 🎨 **响应式设计** - 适配8寸平板横屏显示
- 🌙 **主题切换** - 支持明暗主题切换
- 🔄 **自动刷新** - 定时刷新会议室状态
- 📶 **网络状态** - 实时显示网络连接状态

## 技术栈

- **框架**: Flutter 3.x
- **状态管理**: Provider
- **路由**: GoRouter
- **网络请求**: Dio
- **屏幕适配**: flutter_screenutil
- **二维码**: qr_code_scanner, qr_flutter
- **本地存储**: shared_preferences

## 项目结构

```
lib/
├── main.dart                 # 应用入口
├── models/                   # 数据模型
│   ├── room.dart            # 会议室模型
│   ├── meeting.dart         # 会议模型
│   └── checkin.dart         # 签到模型
├── providers/               # 状态管理
│   ├── app_provider.dart    # 应用状态
│   ├── room_provider.dart   # 会议室状态
│   └── meeting_provider.dart # 会议状态
├── screens/                 # 页面
│   ├── splash_screen.dart   # 启动页
│   ├── room_panel_screen.dart # 主门牌页
│   ├── settings_screen.dart # 设置页
│   ├── qr_scanner_screen.dart # 扫码页
│   └── meeting_detail_screen.dart # 会议详情页
├── widgets/                 # 组件
│   ├── room_header.dart     # 会议室头部
│   ├── current_meeting_card.dart # 当前会议卡片
│   ├── next_meeting_card.dart # 下一个会议卡片
│   ├── today_meetings_list.dart # 今日会议列表
│   └── qr_code_section.dart # 二维码区域
├── services/               # 服务
│   └── api_service.dart    # API服务
└── utils/                  # 工具
    ├── constants.dart      # 常量
    ├── app_theme.dart      # 主题配置
    └── app_router.dart     # 路由配置
```

## 快速开始

### 1. 环境准备

确保已安装：
- Flutter SDK 3.0+
- Android Studio / VS Code
- Android SDK

### 2. 安装依赖

```bash
cd client
flutter pub get
```

### 3. 生成代码

```bash
flutter packages pub run build_runner build
```

### 4. 运行应用

```bash
# 调试模式
flutter run

# 发布模式
flutter run --release
```

### 5. 构建APK

```bash
# 构建调试版APK
flutter build apk --debug

# 构建发布版APK
flutter build apk --release
```

## 配置说明

### API配置

在 `lib/utils/constants.dart` 中配置后端API地址：

```dart
static const String baseUrl = 'http://your-backend-url:8000';
```

### 屏幕适配

应用针对8寸平板横屏优化，设计尺寸为 1280x800：

```dart
ScreenUtilInit(
  designSize: const Size(1280, 800),
  // ...
)
```

### 权限配置

应用需要以下权限：
- 网络访问权限
- 相机权限（二维码扫描）
- 存储权限
- 保持屏幕常亮权限

## 使用说明

### 1. 首次启动

- 应用启动后会显示启动页面
- 自动检测网络连接状态
- 如果没有配置会议室ID，会跳转到设置页面

### 2. 设置会议室

- 在设置页面输入会议室ID
- 可以切换明暗主题
- 保存后自动跳转到门牌页面

### 3. 门牌显示

- 实时显示会议室状态
- 显示当前会议信息
- 显示下一个会议倒计时
- 显示今日所有会议安排
- 提供二维码签到功能

### 4. 扫码签到

- 点击"扫码签到"按钮
- 使用相机扫描二维码
- 自动完成签到流程

## 部署说明

### 1. 平板配置

- 设置屏幕方向为横屏
- 关闭自动锁屏
- 设置应用为默认启动器（可选）

### 2. 网络配置

- 确保平板能访问后端API
- 配置正确的API地址
- 测试网络连接

### 3. 权限设置

- 授予相机权限
- 授予存储权限
- 允许保持屏幕常亮

## 开发指南

### 添加新页面

1. 在 `lib/screens/` 创建页面文件
2. 在 `lib/utils/app_router.dart` 添加路由
3. 更新导航逻辑

### 添加新组件

1. 在 `lib/widgets/` 创建组件文件
2. 在需要的页面中引入使用

### 状态管理

使用 Provider 进行状态管理：

```dart
// 读取状态
Consumer<RoomProvider>(
  builder: (context, roomProvider, child) {
    return Text(roomProvider.currentRoom?.name ?? '');
  },
)

// 更新状态
context.read<RoomProvider>().loadRoomInfo(roomId);
```

## 故障排除

### 常见问题

1. **网络连接失败**
   - 检查API地址配置
   - 确认网络权限
   - 测试后端服务状态

2. **二维码扫描不工作**
   - 检查相机权限
   - 确认在真机上测试
   - 检查相机硬件

3. **屏幕适配问题**
   - 确认设备分辨率
   - 调整设计尺寸参数
   - 测试不同屏幕密度

### 调试技巧

- 使用 `flutter logs` 查看日志
- 在模拟器中测试基本功能
- 在真机上测试相机功能
- 使用网络抓包工具调试API

## 许可证

MIT License
