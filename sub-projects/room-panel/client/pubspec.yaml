name: room_panel_client
description: 智能门牌客户端应用，适配安卓8寸平板

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # UI组件
  cupertino_icons: ^1.0.2
  material_design_icons_flutter: ^7.0.7296
  
  # 状态管理
  provider: ^6.1.1
  
  # 网络请求
  http: ^1.1.0
  dio: ^5.3.2
  
  # 本地存储
  shared_preferences: ^2.2.2
  
  # 二维码扫描
  qr_code_scanner: ^1.0.1
  qr_flutter: ^4.1.0
  
  # 权限管理
  permission_handler: ^11.0.1
  
  # 设备信息
  device_info_plus: ^9.1.0
  
  # 时间处理
  intl: ^0.18.1
  
  # 路由导航
  go_router: ^12.1.1
  
  # JSON序列化
  json_annotation: ^4.8.1
  
  # 屏幕适配
  flutter_screenutil: ^5.9.0
  
  # 网络连接检测
  connectivity_plus: ^5.0.1
  
  # 日志
  logger: ^2.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  flutter_lints: ^3.0.0
  json_serializable: ^6.7.1
  build_runner: ^2.4.7

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
  
  fonts:
    - family: PingFang
      fonts:
        - asset: assets/fonts/PingFang-Regular.ttf
        - asset: assets/fonts/PingFang-Medium.ttf
          weight: 500
        - asset: assets/fonts/PingFang-Bold.ttf
          weight: 700
