#!/bin/bash

# Flutter项目初始化脚本

echo "🚀 初始化Flutter项目..."

# 检查Flutter是否已安装
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter未安装，请先安装Flutter"
    echo "📖 请参考 FLUTTER_SETUP.md 安装指南"
    exit 1
fi

echo "✅ Flutter已安装"
flutter --version

# 进入客户端目录
cd client

# 检查pubspec.yaml是否存在
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ pubspec.yaml文件不存在"
    exit 1
fi

echo "📦 获取Flutter依赖..."
flutter pub get

# 检查是否需要生成代码
if grep -q "build_runner" pubspec.yaml; then
    echo "🔨 生成代码文件..."
    flutter packages pub run build_runner build --delete-conflicting-outputs
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p assets/images
mkdir -p assets/icons
mkdir -p assets/fonts

# 检查Android配置
echo "🔍 检查Android配置..."
if [ ! -f "android/app/src/main/AndroidManifest.xml" ]; then
    echo "⚠️  Android配置文件不存在，可能需要重新创建Flutter项目"
fi

# 运行Flutter doctor
echo "🩺 检查Flutter环境..."
flutter doctor

# 检查可用设备
echo "📱 检查可用设备..."
flutter devices

echo ""
echo "🎉 Flutter项目初始化完成！"
echo ""
echo "📋 下一步操作："
echo "1. 连接Android设备或启动模拟器"
echo "2. 运行项目: flutter run"
echo "3. 构建APK: flutter build apk"
echo ""
echo "🔧 常用命令："
echo "- 热重载: 在运行时按 'r'"
echo "- 热重启: 在运行时按 'R'"
echo "- 退出: 在运行时按 'q'"
echo ""
