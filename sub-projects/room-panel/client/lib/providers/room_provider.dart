import 'package:flutter/material.dart';
import '../models/room.dart';
import '../models/meeting.dart';
import '../services/api_service.dart';
import '../utils/constants.dart';

class RoomProvider extends ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  Room? _currentRoom;
  Meeting? _currentMeeting;
  Meeting? _nextMeeting;
  List<Meeting> _todayMeetings = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  Room? get currentRoom => _currentRoom;
  Meeting? get currentMeeting => _currentMeeting;
  Meeting? get nextMeeting => _nextMeeting;
  List<Meeting> get todayMeetings => _todayMeetings;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isRoomAvailable => _currentMeeting == null;

  // 加载会议室信息
  Future<void> loadRoomInfo(String roomId) async {
    _setLoading(true);
    _clearError();

    try {
      // 并行加载会议室信息和状态
      final futures = await Future.wait([
        _apiService.getRoom(roomId),
        _apiService.getRoomStatus(roomId),
        _apiService.getRoomMeetingsToday(roomId),
      ]);

      _currentRoom = futures[0] as Room;
      final statusData = futures[1] as Map<String, dynamic>;
      _todayMeetings = futures[2] as List<Meeting>;

      // 解析状态数据
      _currentMeeting = statusData['current_meeting'] != null
          ? Meeting.fromJson(statusData['current_meeting'])
          : null;
      
      _nextMeeting = statusData['next_meeting'] != null
          ? Meeting.fromJson(statusData['next_meeting'])
          : null;

      // 更新会议室状态
      if (_currentRoom != null) {
        final isAvailable = statusData['is_available'] ?? true;
        _currentRoom = _currentRoom!.copyWith(
          status: isAvailable ? AppConstants.roomStatusAvailable : AppConstants.roomStatusOccupied,
        );
      }

    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // 刷新会议室状态
  Future<void> refreshRoomStatus() async {
    if (_currentRoom == null) return;

    try {
      final statusData = await _apiService.getRoomStatus(_currentRoom!.id.toString());
      
      _currentMeeting = statusData['current_meeting'] != null
          ? Meeting.fromJson(statusData['current_meeting'])
          : null;
      
      _nextMeeting = statusData['next_meeting'] != null
          ? Meeting.fromJson(statusData['next_meeting'])
          : null;

      // 更新会议室状态
      final isAvailable = statusData['is_available'] ?? true;
      _currentRoom = _currentRoom!.copyWith(
        status: isAvailable ? AppConstants.roomStatusAvailable : AppConstants.roomStatusOccupied,
      );

      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  // 刷新今日会议
  Future<void> refreshTodayMeetings() async {
    if (_currentRoom == null) return;

    try {
      _todayMeetings = await _apiService.getRoomMeetingsToday(_currentRoom!.id.toString());
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  // 获取会议室状态文本
  String get roomStatusText {
    if (_currentRoom == null) return '未知';
    
    if (_currentMeeting != null) {
      return '使用中 - ${_currentMeeting!.title}';
    } else if (_nextMeeting != null) {
      final minutesUntilNext = _nextMeeting!.startTime.difference(DateTime.now()).inMinutes;
      if (minutesUntilNext <= 15) {
        return '即将开始 - ${_nextMeeting!.title}';
      }
    }
    
    return _currentRoom!.statusText;
  }

  // 获取会议室状态颜色
  Color get roomStatusColor {
    if (_currentRoom == null) return Colors.grey;
    
    if (_currentMeeting != null) {
      return const Color(AppConstants.errorColor);
    } else if (_nextMeeting != null) {
      final minutesUntilNext = _nextMeeting!.startTime.difference(DateTime.now()).inMinutes;
      if (minutesUntilNext <= 15) {
        return const Color(AppConstants.warningColor);
      }
    }
    
    return Color(_currentRoom!.statusColor);
  }

  // 获取下一个会议的倒计时文本
  String? get nextMeetingCountdown {
    if (_nextMeeting == null) return null;
    
    final now = DateTime.now();
    final diff = _nextMeeting!.startTime.difference(now);
    
    if (diff.isNegative) return null;
    
    final hours = diff.inHours;
    final minutes = diff.inMinutes % 60;
    
    if (hours > 0) {
      return '${hours}小时${minutes}分钟后开始';
    } else {
      return '${minutes}分钟后开始';
    }
  }

  // 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // 设置错误
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  // 清除错误
  void _clearError() {
    _error = null;
    notifyListeners();
  }

  // 清除数据
  void clear() {
    _currentRoom = null;
    _currentMeeting = null;
    _nextMeeting = null;
    _todayMeetings = [];
    _isLoading = false;
    _error = null;
    notifyListeners();
  }
}
