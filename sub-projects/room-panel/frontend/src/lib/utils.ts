import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date | string) {
  const d = new Date(date)
  return d.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}

export function formatTime(date: Date | string) {
  const d = new Date(date)
  return d.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
  })
}

export function formatDateTime(date: Date | string) {
  const d = new Date(date)
  return d.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

export function formatTimeRange(startTime: Date | string, endTime: Date | string) {
  return `${formatTime(startTime)} - ${formatTime(endTime)}`
}

export function getRoomStatusText(status: string) {
  switch (status) {
    case 'available':
      return '空闲'
    case 'occupied':
      return '使用中'
    case 'maintenance':
      return '维护中'
    default:
      return '未知'
  }
}

export function getMeetingStatusText(status: string) {
  switch (status) {
    case 'scheduled':
      return '已安排'
    case 'ongoing':
      return '进行中'
    case 'completed':
      return '已结束'
    case 'cancelled':
      return '已取消'
    default:
      return '未知'
  }
}

export function getRoomStatusColor(status: string) {
  switch (status) {
    case 'available':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'occupied':
      return 'bg-red-100 text-red-800 border-red-200'
    case 'maintenance':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

export function getMeetingStatusColor(status: string) {
  switch (status) {
    case 'scheduled':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'ongoing':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'completed':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    case 'cancelled':
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

export function calculateDuration(startTime: Date | string, endTime: Date | string) {
  const start = new Date(startTime)
  const end = new Date(endTime)
  const diffMs = end.getTime() - start.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  
  if (diffMins < 60) {
    return `${diffMins}分钟`
  } else {
    const hours = Math.floor(diffMins / 60)
    const mins = diffMins % 60
    return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`
  }
}

export function isToday(date: Date | string) {
  const d = new Date(date)
  const today = new Date()
  return d.toDateString() === today.toDateString()
}

export function isMeetingOngoing(startTime: Date | string, endTime: Date | string) {
  const now = new Date()
  const start = new Date(startTime)
  const end = new Date(endTime)
  return now >= start && now <= end
}

export function isMeetingStartingSoon(startTime: Date | string, minutesBefore: number = 15) {
  const now = new Date()
  const start = new Date(startTime)
  const diffMs = start.getTime() - now.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  return diffMins > 0 && diffMins <= minutesBefore
}
