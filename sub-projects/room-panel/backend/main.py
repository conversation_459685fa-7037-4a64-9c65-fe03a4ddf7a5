from fastapi import FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import H<PERSON><PERSON><PERSON>earer
import uvicorn
import os
from dotenv import load_dotenv

from app.database import engine, Base
from app.routers import rooms, meetings, auth, qrcode_router
from app.config import settings

# 加载环境变量
load_dotenv()

# 创建数据库表
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="智能门牌系统API",
    description="智能门牌系统后端API，支持会议室管理、预定查询、扫码签到等功能",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(auth.router, prefix="/api/v1/auth", tags=["认证"])
app.include_router(rooms.router, prefix="/api/v1/rooms", tags=["会议室"])
app.include_router(meetings.router, prefix="/api/v1/meetings", tags=["会议"])
app.include_router(qrcode_router.router, prefix="/api/v1/qrcode", tags=["二维码"])

@app.get("/")
async def root():
    return {"message": "智能门牌系统API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG
    )
