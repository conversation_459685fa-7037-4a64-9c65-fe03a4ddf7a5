# Flutter环境安装指南

由于网络下载速度较慢，建议您手动安装Flutter环境。以下是详细的安装步骤：

## 方法一：官方安装（推荐）

### 1. 下载Flutter SDK

访问Flutter官网下载页面：
- **官网**: https://flutter.dev/docs/get-started/install/macos
- **国内镜像**: https://flutter.cn/docs/get-started/install/macos

根据您的系统选择对应版本：
- **macOS (Apple Silicon)**: flutter_macos_arm64_3.16.5-stable.zip
- **macOS (Intel)**: flutter_macos_3.16.5-stable.zip

### 2. 解压和安装

```bash
# 下载完成后，解压到用户目录
cd ~/
unzip ~/Downloads/flutter_macos_*_3.16.5-stable.zip

# 添加到PATH环境变量
echo 'export PATH="$PATH:$HOME/flutter/bin"' >> ~/.zshrc
source ~/.zshrc
```

### 3. 验证安装

```bash
flutter --version
flutter doctor
```

## 方法二：使用包管理器

### 使用Homebrew安装

```bash
# 安装Homebrew (如果还没安装)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装Flutter
brew install --cask flutter
```

## 配置开发环境

### 1. 安装Android Studio

下载并安装Android Studio：
- 官网: https://developer.android.com/studio
- 安装Android SDK和Android SDK Command-line Tools

### 2. 配置Android环境

```bash
# 添加Android SDK到PATH
echo 'export ANDROID_HOME=$HOME/Library/Android/sdk' >> ~/.zshrc
echo 'export PATH=$PATH:$ANDROID_HOME/emulator' >> ~/.zshrc
echo 'export PATH=$PATH:$ANDROID_HOME/tools' >> ~/.zshrc
echo 'export PATH=$PATH:$ANDROID_HOME/tools/bin' >> ~/.zshrc
echo 'export PATH=$PATH:$ANDROID_HOME/platform-tools' >> ~/.zshrc
source ~/.zshrc
```

### 3. 接受Android许可证

```bash
flutter doctor --android-licenses
```

### 4. 安装iOS开发工具（可选）

```bash
# 安装Xcode (从App Store)
# 安装CocoaPods
sudo gem install cocoapods
```

## 初始化项目

安装完Flutter后，运行以下命令初始化项目：

```bash
cd sub-projects/room-panel
./init-flutter-project.sh
```

## 验证环境

运行Flutter doctor检查环境：

```bash
flutter doctor -v
```

期望看到类似输出：
```
✓ Flutter (Channel stable, 3.16.5, on macOS 14.0 23A344 darwin-arm64, locale zh-Hans-CN)
✓ Android toolchain - develop for Android devices
✓ Xcode - develop for iOS and macOS
✓ Chrome - develop for the web
✓ Android Studio
✓ VS Code
✓ Connected device
✓ Network resources
```

## 常见问题解决

### 1. Flutter命令未找到
```bash
# 检查PATH配置
echo $PATH | grep flutter

# 重新加载配置
source ~/.zshrc
```

### 2. Android许可证问题
```bash
flutter doctor --android-licenses
# 输入 'y' 接受所有许可证
```

### 3. iOS开发环境问题
```bash
# 安装Xcode命令行工具
sudo xcode-select --install

# 同意Xcode许可证
sudo xcodebuild -license accept
```

### 4. 网络问题
如果遇到网络问题，可以配置国内镜像：

```bash
export PUB_HOSTED_URL=https://pub.flutter-io.cn
export FLUTTER_STORAGE_BASE_URL=https://storage.flutter-io.cn
```

## 项目初始化

Flutter环境配置完成后，运行项目初始化：

```bash
cd client
flutter pub get
flutter packages pub run build_runner build --delete-conflicting-outputs
```

## 运行项目

```bash
# 查看可用设备
flutter devices

# 运行到Android设备/模拟器
flutter run

# 构建APK
flutter build apk --release
```

## 开发工具推荐

1. **Android Studio** - 官方IDE，功能完整
2. **VS Code** - 轻量级，插件丰富
3. **IntelliJ IDEA** - JetBrains出品

安装Flutter插件：
- Flutter
- Dart
- Android iOS Emulator

## 下一步

环境配置完成后，您可以：

1. 运行Flutter项目：`cd client && flutter run`
2. 查看项目文档：`client/README.md`
3. 开始开发和调试

如有问题，请参考：
- Flutter官方文档: https://flutter.dev/docs
- Flutter中文网: https://flutter.cn
- 项目README: `client/README.md`
