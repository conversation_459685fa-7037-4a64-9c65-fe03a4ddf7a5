import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../providers/app_provider.dart';
import '../utils/constants.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final _roomIdController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    final currentRoomId = context.read<AppProvider>().currentRoomId;
    if (currentRoomId != null) {
      _roomIdController.text = currentRoomId;
    }
  }

  @override
  void dispose() {
    _roomIdController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
        centerTitle: true,
      ),
      body: Center(
        child: Container(
          width: 600.w,
          padding: EdgeInsets.all(32.w),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 标题
                Text(
                  '智能门牌系统设置',
                  style: Theme.of(context).textTheme.headlineLarge,
                  textAlign: TextAlign.center,
                ),
                
                SizedBox(height: 48.h),
                
                // 会议室ID输入
                TextFormField(
                  controller: _roomIdController,
                  decoration: const InputDecoration(
                    labelText: '会议室ID',
                    hintText: '请输入会议室ID',
                    prefixIcon: Icon(Icons.meeting_room),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请输入会议室ID';
                    }
                    return null;
                  },
                ),
                
                SizedBox(height: 32.h),
                
                // 主题切换
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.w),
                    child: Row(
                      children: [
                        Icon(
                          Icons.palette,
                          size: 24.sp,
                        ),
                        SizedBox(width: 16.w),
                        Expanded(
                          child: Text(
                            '深色主题',
                            style: Theme.of(context).textTheme.bodyLarge,
                          ),
                        ),
                        Consumer<AppProvider>(
                          builder: (context, appProvider, child) {
                            return Switch(
                              value: appProvider.themeMode == ThemeMode.dark,
                              onChanged: (value) {
                                appProvider.setThemeMode(
                                  value ? ThemeMode.dark : ThemeMode.light,
                                );
                              },
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                
                SizedBox(height: 32.h),
                
                // 网络状态
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.w),
                    child: Row(
                      children: [
                        Consumer<AppProvider>(
                          builder: (context, appProvider, child) {
                            return Icon(
                              appProvider.isOnline ? Icons.wifi : Icons.wifi_off,
                              size: 24.sp,
                              color: appProvider.isOnline ? Colors.green : Colors.red,
                            );
                          },
                        ),
                        SizedBox(width: 16.w),
                        Expanded(
                          child: Consumer<AppProvider>(
                            builder: (context, appProvider, child) {
                              return Text(
                                appProvider.isOnline ? '网络已连接' : '网络连接失败',
                                style: Theme.of(context).textTheme.bodyLarge,
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                
                SizedBox(height: 48.h),
                
                // 操作按钮
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          if (context.canPop()) {
                            context.pop();
                          } else {
                            context.go('/');
                          }
                        },
                        child: const Text('取消'),
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _saveSettings,
                        child: const Text('保存'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _saveSettings() {
    if (_formKey.currentState!.validate()) {
      final roomId = _roomIdController.text.trim();
      context.read<AppProvider>().setCurrentRoomId(roomId);
      
      // 跳转到门牌页面
      context.go('/room-panel/$roomId');
      
      // 显示保存成功提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('设置已保存'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }
}
