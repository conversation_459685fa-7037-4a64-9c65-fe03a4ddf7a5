import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

class MeetingDetailScreen extends StatelessWidget {
  final String meetingId;

  const MeetingDetailScreen({
    super.key,
    required this.meetingId,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('会议详情'),
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: const Icon(Icons.arrow_back),
        ),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.meeting_room,
              size: 80.sp,
              color: Colors.grey,
            ),
            SizedBox(height: 16.h),
            Text(
              '会议详情页面',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            Sized<PERSON>ox(height: 8.h),
            Text(
              '会议ID: $meetingId',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Si<PERSON><PERSON><PERSON>(height: 32.h),
            ElevatedButton(
              onPressed: () => context.pop(),
              child: const Text('返回'),
            ),
          ],
        ),
      ),
    );
  }
}
