import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../providers/room_provider.dart';
import '../utils/constants.dart';

class NextMeetingCard extends StatelessWidget {
  const NextMeetingCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<RoomProvider>(
      builder: (context, roomProvider, child) {
        final nextMeeting = roomProvider.nextMeeting;
        
        if (nextMeeting == null) {
          return const SizedBox.shrink();
        }
        
        return Card(
          child: Container(
            height: 120.h,
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.schedule,
                      size: 20.sp,
                      color: const Color(AppConstants.warningColor),
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      '下一个会议',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: const Color(AppConstants.warningColor),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                
                SizedBox(height: 12.h),
                
                Text(
                  nextMeeting.title,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                
                SizedBox(height: 8.h),
                
                Row(
                  children: [
                    Text(
                      nextMeeting.timeRange,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const Spacer(),
                    if (roomProvider.nextMeetingCountdown != null)
                      Text(
                        roomProvider.nextMeetingCountdown!,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: const Color(AppConstants.warningColor),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
