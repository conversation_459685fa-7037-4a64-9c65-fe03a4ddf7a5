import 'package:json_annotation/json_annotation.dart';
import 'meeting.dart';

part 'checkin.g.dart';

@JsonSerializable()
class CheckIn {
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'meeting_id')
  final int meetingId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_name')
  final String userName;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id')
  final String? userId;
  @<PERSON>son<PERSON>ey(name: 'checkin_time')
  final DateTime checkinTime;
  @<PERSON>sonKey(name: 'checkin_method')
  final String checkinMethod;
  final Meeting? meeting;

  CheckIn({
    required this.id,
    required this.meetingId,
    required this.userName,
    this.userId,
    required this.checkinTime,
    required this.checkinMethod,
    this.meeting,
  });

  factory CheckIn.fromJson(Map<String, dynamic> json) => _$CheckInFromJson(json);
  Map<String, dynamic> toJson() => _$CheckInToJson(this);

  // 获取签到方式显示文本
  String get checkinMethodText {
    switch (checkinMethod) {
      case 'qrcode':
        return '扫码签到';
      case 'manual':
        return '手动签到';
      default:
        return '未知方式';
    }
  }

  // 格式化签到时间
  String get formattedCheckinTime {
    return '${checkinTime.hour.toString().padLeft(2, '0')}:${checkinTime.minute.toString().padLeft(2, '0')}';
  }

  // 复制对象
  CheckIn copyWith({
    int? id,
    int? meetingId,
    String? userName,
    String? userId,
    DateTime? checkinTime,
    String? checkinMethod,
    Meeting? meeting,
  }) {
    return CheckIn(
      id: id ?? this.id,
      meetingId: meetingId ?? this.meetingId,
      userName: userName ?? this.userName,
      userId: userId ?? this.userId,
      checkinTime: checkinTime ?? this.checkinTime,
      checkinMethod: checkinMethod ?? this.checkinMethod,
      meeting: meeting ?? this.meeting,
    );
  }

  @override
  String toString() {
    return 'CheckIn{id: $id, userName: $userName, checkinTime: $formattedCheckinTime}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CheckIn && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

// 签到请求模型
@JsonSerializable()
class CheckInRequest {
  @JsonKey(name: 'meeting_id')
  final int meetingId;
  @JsonKey(name: 'user_name')
  final String userName;
  @JsonKey(name: 'user_id')
  final String? userId;
  @JsonKey(name: 'checkin_method')
  final String checkinMethod;

  CheckInRequest({
    required this.meetingId,
    required this.userName,
    this.userId,
    this.checkinMethod = 'qrcode',
  });

  factory CheckInRequest.fromJson(Map<String, dynamic> json) => _$CheckInRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CheckInRequestToJson(this);
}
