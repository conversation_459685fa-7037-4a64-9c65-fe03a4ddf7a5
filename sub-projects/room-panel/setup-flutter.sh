#!/bin/bash

# Flutter环境安装和初始化脚本

echo "🚀 开始初始化Flutter环境..."

# 检测系统架构
ARCH=$(uname -m)
OS=$(uname -s)

echo "系统信息: $OS $ARCH"

# 设置Flutter版本和下载URL
FLUTTER_VERSION="3.16.5"
if [[ "$OS" == "Darwin" ]]; then
    if [[ "$ARCH" == "arm64" ]]; then
        FLUTTER_URL="https://storage.googleapis.com/flutter_infra_release/releases/stable/macos/flutter_macos_arm64_${FLUTTER_VERSION}-stable.zip"
    else
        FLUTTER_URL="https://storage.googleapis.com/flutter_infra_release/releases/stable/macos/flutter_macos_${FLUTTER_VERSION}-stable.zip"
    fi
    FLUTTER_DIR="$HOME/flutter"
elif [[ "$OS" == "Linux" ]]; then
    FLUTTER_URL="https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_${FLUTTER_VERSION}-stable.tar.xz"
    FLUTTER_DIR="$HOME/flutter"
else
    echo "❌ 不支持的操作系统: $OS"
    exit 1
fi

# 检查Flutter是否已安装
if command -v flutter &> /dev/null; then
    echo "✅ Flutter已安装"
    flutter --version
else
    echo "📦 开始下载和安装Flutter..."
    
    # 创建临时目录
    TEMP_DIR=$(mktemp -d)
    cd "$TEMP_DIR"
    
    # 下载Flutter
    echo "下载Flutter $FLUTTER_VERSION..."
    if [[ "$OS" == "Darwin" ]]; then
        curl -L "$FLUTTER_URL" -o flutter.zip
        unzip -q flutter.zip
        rm flutter.zip
    else
        curl -L "$FLUTTER_URL" -o flutter.tar.xz
        tar -xf flutter.tar.xz
        rm flutter.tar.xz
    fi
    
    # 移动到目标目录
    if [ -d "$FLUTTER_DIR" ]; then
        echo "删除现有Flutter目录..."
        rm -rf "$FLUTTER_DIR"
    fi
    
    mv flutter "$FLUTTER_DIR"
    
    # 添加到PATH
    echo "配置环境变量..."
    
    # 检查shell类型
    if [[ "$SHELL" == *"zsh"* ]]; then
        PROFILE_FILE="$HOME/.zshrc"
    else
        PROFILE_FILE="$HOME/.bash_profile"
    fi
    
    # 添加Flutter到PATH
    if ! grep -q "flutter/bin" "$PROFILE_FILE" 2>/dev/null; then
        echo "" >> "$PROFILE_FILE"
        echo "# Flutter" >> "$PROFILE_FILE"
        echo "export PATH=\"\$PATH:$FLUTTER_DIR/bin\"" >> "$PROFILE_FILE"
        echo "已添加Flutter到 $PROFILE_FILE"
    fi
    
    # 临时添加到当前会话
    export PATH="$PATH:$FLUTTER_DIR/bin"
    
    # 清理临时文件
    cd - > /dev/null
    rm -rf "$TEMP_DIR"
    
    echo "✅ Flutter安装完成"
fi

# 验证Flutter安装
echo "🔍 验证Flutter安装..."
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter命令未找到，请手动添加到PATH或重新启动终端"
    echo "Flutter安装路径: $FLUTTER_DIR/bin"
    exit 1
fi

# 运行Flutter doctor
echo "🩺 运行Flutter doctor检查..."
flutter doctor

# 检查Android工具链
echo "📱 检查Android开发环境..."
if ! flutter doctor | grep -q "Android toolchain"; then
    echo "⚠️  Android工具链未配置，请安装Android Studio"
    echo "下载地址: https://developer.android.com/studio"
fi

# 初始化Flutter项目
echo "🔧 初始化Flutter项目..."
cd client

# 检查pubspec.yaml是否存在
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ pubspec.yaml文件不存在，请检查项目结构"
    exit 1
fi

# 获取依赖
echo "📦 获取Flutter依赖..."
flutter pub get

# 生成代码
echo "🔨 生成代码文件..."
if grep -q "build_runner" pubspec.yaml; then
    flutter packages pub run build_runner build --delete-conflicting-outputs
fi

# 检查连接的设备
echo "📱 检查可用设备..."
flutter devices

echo ""
echo "🎉 Flutter环境初始化完成！"
echo ""
echo "📋 下一步操作："
echo "1. 如果是新终端会话，请运行: source ~/.zshrc 或 source ~/.bash_profile"
echo "2. 安装Android Studio (如果还没安装): https://developer.android.com/studio"
echo "3. 配置Android SDK和模拟器"
echo "4. 连接Android设备或启动模拟器"
echo "5. 运行项目: cd client && flutter run"
echo ""
echo "🔧 常用命令："
echo "- 检查环境: flutter doctor"
echo "- 查看设备: flutter devices"
echo "- 运行项目: flutter run"
echo "- 构建APK: flutter build apk"
echo ""
